package com.wxy.system.common.dto.university.record;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.List;

/**
 * 企业大学-批阅信息和抽检(UniversityReadOverSpotCheck)查询DTO
 *
 * <AUTHOR>
 * @since 2024-09-20 17:07:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UniversityReadOverSpotCheckFindDTO  implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 查询条件
     */
    @ApiModelProperty("查询条件")
    private String keywords;

    @ApiModelProperty("记录id")
    private String recordId;

    @ApiModelProperty("抽检人员工id")
    private String userId;

    @ApiModelProperty("学员部门id列表")
    List<String> deptIdList;

    @ApiModelProperty("门店id列表")
    List<String> shopIdList;

    @ApiModelProperty("学员员工id列表")
    private List<String> userIdList;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty(value = "查询类型(0-待批阅;1-已批阅;2-全部)", example = "0")
    private Integer queryType;

    @ApiModelProperty(value = "批阅类型(1-考试;2-作业)", example = "1")
    private Integer readOverType;
}

