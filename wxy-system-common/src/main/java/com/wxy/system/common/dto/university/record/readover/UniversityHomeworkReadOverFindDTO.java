package com.wxy.system.common.dto.university.record.readover;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 企业大学-批阅信息和抽检查询DTO
 *
 * <AUTHOR>
 * @since 2024-10-09 17:07:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UniversityHomeworkReadOverFindDTO implements Serializable{
  
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("记录id")
    private String recordId;

    @ApiModelProperty("抽检人员工id")
    private String userId;

    @ApiModelProperty("学员部门id列表")
    List<String> deptIdList;

    @ApiModelProperty("门店id列表")
    List<String> shopIdList;

    @ApiModelProperty("学员员工id列表")
    private List<String> userIdList;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty(value = "查询类型(0-未审批;1-已审批;2全部)", example = "0")
    private Integer queryType;

    @ApiModelProperty("keywords")
    private String keywords;
}
    
