package com.wxy.system.common.dto.university.record.spotcheck;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 企业大学-抽检(UniversitySpotCheck)查询DTO
 *
 * <AUTHOR>
 * @since 2024-10-08 10:51:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UniversitySpotCheckFindDTO  implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程Id/项目id")
    private String activityId;

    @ApiModelProperty("记录id")
    private String recordId;

    @ApiModelProperty("抽检人员工id")
    private String userId;

    @ApiModelProperty("学员部门id列表")
    List<String> deptIdList;

    @ApiModelProperty("门店id列表")
    List<String> shopIdList;

    @ApiModelProperty("学员员工id列表")
    private List<String> userIdList;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("循环时间")
    private String loopDate;

    @ApiModelProperty(value = "查询类型(0-待完成;1-已完成)", example = "0")
    private Integer queryType;

    @ApiModelProperty(value = "项目排期(1-指定日期模式;2-随到随学模式;3-循环模式;)", example = "3")
    private Integer projectScheduling;
}

