<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wxy.system.server.dao.university.record.UniversityReadOverSpotCheckDao">

    <sql id="AllColumns">
        a.id AS id,
        a.record_id AS recordId,
        a.read_over_type AS readOverType,
        a.operate_type AS operateType,
        a.read_over_user AS readOverUser,
        a.read_over_user_name AS readOverUserName,
        a.read_over_time AS readOverTime,
        a.read_over_score AS readOverScore,
        a.read_over_comment AS readOverComment,
        a.read_over_status AS readOverStatus
    </sql>
    <select id="getById" resultType="com.wxy.system.server.po.university.record.UniversityReadOverSpotCheck">
		SELECT
			<include refid="AllColumns"/>
		FROM university_read_over_spot_check a
		WHERE a.id = #{id} and a.is_valid = 1
	</select>

    <select id="findPage" resultType="com.wxy.system.server.po.university.record.UniversityReadOverSpotCheck" parameterType="com.wxy.system.common.dto.university.record.UniversityReadOverSpotCheckFindDTO">
		SELECT
			<include refid="AllColumns"/>
		FROM university_read_over_spot_check a
		<where>
			1=1 and a.is_valid = 1
			<if test="dto.keywords != null and dto.keywords != ''">
              and (a.record_id like  concat('%',#{dto.keywords},'%') or a.read_over_user like  concat('%',#{dto.keywords},'%')   )
            </if>
            <if test="dto.recordId != null and dto.recordId != ''">
                and a.record_id = #{dto.recordId}
            </if>
            <if test="dto.userId != null and dto.userId != ''">
                and a.read_over_user = #{dto.userId}
            </if>
            <if test="dto.readOverType != null">
                and a.read_over_type = #{dto.readOverType}
            </if>
            <if test="dto.queryType != null">
                <choose>
                    <when test="dto.queryType == 0">
                        <!-- 待批阅：批阅状态为0或1 -->
                        and a.read_over_status in (0, 1)
                    </when>
                    <when test="dto.queryType == 1">
                        <!-- 已批阅：批阅状态为2或3 -->
                        and a.read_over_status in (2, 3)
                    </when>
                    <!-- queryType == 2 时查询全部，不添加条件 -->
                </choose>
            </if>
		</where>
        order by a.create_time desc
	</select>
    <select id="teachRecordList" resultType="com.wxy.system.common.vo.university.record.UniversityTeachRecordVo">
        select
            distinct
            b.id AS teachId,
            b.course_title As  teachName,
            a.sys_user_id AS sysUserId,
            2            AS teachType,
            b.study_profile_id AS fileId,
            b.create_time AS createTime
        from university_lecturer a
             left join university_course b on a.course_id = b.id
             left join university_course_participant_task  c on c.sys_user_id = a.sys_user_id and b.id = c.activity_id
        where a.is_valid = 1 and b.status = 1
            and b.is_valid = 1
            and c.is_valid = 1
            and c.task_type = 2
            and a.sys_user_id = #{userId}
            <if test="statusList != null and statusList.size() > 0">
                and c.task_status in
                <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        UNION All
        select
            distinct
            b.id AS teachId,
            b.project_name As  teachName,
            a.sys_user_id AS sysUserId,
            2            AS teachType,
            b.project_file_id AS fileId,
            b.create_time AS createTime
        from university_training_students a
             left join university_project b on a.project_id = b.id
             left join university_course_participant_task  c on c.sys_user_id = a.sys_user_id and b.id = c.activity_id
        where a.is_valid = 1 and b.project_status = 1
           and b.is_valid = 1
           and c.is_valid = 1
           and c.task_type = 1
           and a.trainer_id =  #{userId}
            <if test="statusList != null and statusList.size() > 0">
                and c.task_status in
                <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>

    </select>
    <select id="findCourseLit" resultType="com.wxy.system.common.vo.university.record.UniversityTeachRecordVo">
        SELECT
            DISTINCT
            c.id AS teachId,
            c.course_title AS teachName,
            1 AS teachType,
            c.study_profile_id AS fileId,
            e.sys_user_id AS sys_user_id,
            a.id AS taskId,
            f.task_status AS taskStatus,
            f.progress AS progress,
            c.create_time AS createTime
        FROM university_task a
            INNER JOIN university_correlation_task b ON a.id = b.task_id
            INNER JOIN university_course c ON b.correlation_object_id = c.id
            INNER JOIN university_lecturer d ON c.id = d.course_id
            INNER JOIN university_course_participant_task e on c.id = e.activity_id
            INNER JOIN university_course_participant_task_det f on  e.id = f.participant_task_id and a.id = f.detail_id
            INNER JOIN sys_user g on e.sys_user_id  = g.id
        WHERE
            a.is_valid = 1
          AND c.is_valid = 1
          AND d.is_valid = 1
          and e.is_valid = 1
          and f.is_valid = 1
          AND c.STATUS = 1
          AND a.need_teach = 1
          and d.sys_user_id = #{userId}
          and g.user_state = 1
          and g.induction_state != 29
          and g.user_number_type = 0
        <if test="courseId != null and courseId != ''">
            and c.id = #{courseId}
        </if>
        order by c.create_time asc

    </select>
    <select id="findProjectList" resultType="com.wxy.system.common.vo.university.record.UniversityTeachRecordVo">
        select
            distinct
            a.id AS  teachId,
            a.project_name As  teachName,
            b.project_plan_begin_date AS projectPlanBeginDate,
            b.project_plan_end_date AS projectPlanEndDate,
            b.project_plan_day_begin  AS projectPlanDayBegin,
            b.project_plan_day_end  AS projectPlanDayEnd,
            2  AS teachType,
            a.project_file_id AS fileId,
            e.id AS taskId,
            p.detail_id AS courseId,
            g.sys_user_id AS sys_user_id,
            h.progress AS progress,
            h.task_status AS taskStatus,
            a.create_time AS createTime
        from university_project a
            inner join university_project_plan b on a.id = b.project_id
            inner join university_course c on  b.project_object_id = c.id
            inner join university_correlation_task d on c.id = d.correlation_object_id
            inner join university_task e on d.task_id = e.id
            inner join university_training_students f on f.project_id = a.id
            INNER JOIN university_course_participant_task g on a.id = g.activity_id
            INNER JOIN university_course_participant_task_det h on  g.id = h.participant_task_id and e.id = h.detail_id
            LEFT JOIN university_course_participant_task_det p on h.parent_id = p.id
            INNER JOIN sys_user i on g.sys_user_id  = i.id
        where a.is_valid = 1
          and b.is_valid = 1
          and c.is_valid = 1
          and e.is_valid = 1
          and f.is_valid = 1
          and g.is_valid = 1
          and h.is_valid = 1
          and e.need_teach = 1
          and b.project_plan_type = 1
          and h.parent_id is not null
          and f.trainer_id =  #{userId}
          and i.user_state = 1
          and i.induction_state != 29
          and i.user_number_type = 0
        <if test="projectId != null and projectId != ''">
            and a.id = #{projectId}
        </if>
        order by a.create_time asc
    </select>
    <select id="findReadOverList" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        select
            distinct
            case when a.teach_tool = 5 then h.id  else i.id end AS recordId,
            a.teach_tool AS teachTool,
            a.id AS taskId,
            a.task_name AS taskName,
            e.sys_user_id AS userId,
            g.user_name AS userName,
            case when a.teach_tool = 5 then h.exam_end_time else i.homework_end_time end AS recordTime
        from  university_task a
            INNER JOIN university_correlation_task b ON a.id = b.task_id
            INNER JOIN university_course c ON b.correlation_object_id = c.id
            INNER JOIN university_lecturer d ON c.id = d.course_id
            INNER JOIN university_course_participant_task e on c.id = e.activity_id
            INNER JOIN university_course_participant_task_det f on  e.id = f.participant_task_id and a.id = f.detail_id
            INNER JOIN sys_user g on e.sys_user_id  = g.id
            INNER JOIN university_test_paper_record h on f.id = h.user_task_det_id
            INNER JOIN university_homework_record i on f.id = i.user_task_det_id
        where
            a.is_valid = 1
            AND c.is_valid = 1
            AND d.is_valid = 1
            and e.is_valid = 1
            and f.is_valid = 1
            AND c.STATUS = 1
            AND d.sys_user_id = #{dto.userId}
            AND g.user_state = 1
            AND g.induction_state != 29
            AND g.user_number_type = 0
            AND h.test_paper_status = 2
            AND h.read_over_status in(1,2)
            <if test="dto.keywords != null and dto.keywords != ''">
                AND  a.task_name  like  concat('%',#{dto.keywords},'%')
            </if>
        union all
        SELECT
            DISTINCT
            case when e.teach_tool = 5 then j.id  else k.id end AS recordId,
            e.teach_tool AS teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            g.sys_user_id AS userId,
            i.user_name AS userName,
            case when e.teach_tool = 5 then j.exam_end_time else k.homework_end_time end AS recordTime
        FROM  university_project a
            INNER JOIN university_project_plan b ON a.id = b.project_id
            INNER JOIN university_course c ON b.project_object_id = c.id
            INNER JOIN university_correlation_task d ON c.id = d.correlation_object_id
            INNER JOIN university_task e ON d.task_id = e.id
            INNER JOIN university_training_students f ON f.project_id = a.id
            INNER JOIN university_course_participant_task g ON c.id = g.activity_id
            INNER JOIN university_course_participant_task_det h ON g.id = h.participant_task_id  AND e.id = h.detail_id
            INNER JOIN sys_user i ON g.sys_user_id = i.id
            INNER JOIN university_test_paper_record j on h.id = j.user_task_det_id
            INNER JOIN university_homework_record k on h.id = k.user_task_det_id
        WHERE
            a.is_valid = 1
            AND b.is_valid = 1
            AND c.is_valid = 1
            AND e.is_valid = 1
            AND f.is_valid = 1
            and g.is_valid = 1
            and h.is_valid = 1
            AND e.need_teach = 1
            AND b.project_plan_type = 1
            AND h.parent_id IS NULL
            AND f.trainer_id = #{dto.userId}
            AND i.user_state = 1
            AND i.induction_state != 29
            AND i.user_number_type = 0
            AND j.test_paper_status = 2
            AND j.read_over_status in(1,2)
            <if test="dto.keywords != null and dto.keywords != ''">
                AND  e.task_name  like  concat('%',#{dto.keywords},'%')
            </if>
    </select>
    <select id="getByRecordId" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverSpotCheckVo">
        SELECT
            a.id AS id,
            a.record_id AS recordId,
            a.read_over_type AS readOverType,
            a.read_over_user AS readOverUser,
            a.read_over_user_name AS readOverUserName,
            a.read_over_time AS readOverTime,
            a.read_over_score AS readOverScore,
            a.read_over_comment AS readOverComment,
            a.read_over_status AS readOverStatus
        FROM university_read_over_spot_check a
        WHERE a.record_id = #{recordId} and a.is_valid = 1
    </select>
    <select id="findUnReadOverExamList" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        select
            distinct
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            0  AS recordStatus
        from university_read_over_spot_check_user a
                 inner join university_test_paper_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 inner join university_course f on d.activity_id = f.id
                 inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.record_type = 1
          and b.read_over_status = 0
          and d.task_type = 0
          and e.is_valid = 1
          and e.teach_tool = 5
          and f.course_type = 1
          and a.sys_user_id = #{dto.userId}
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.keywords != null and dto.keywords != ''">
            AND  e.task_name  like  concat('%',#{dto.keywords},'%')
        </if>

        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL
        select
            distinct
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            0  AS recordStatus
        from university_read_over_spot_check_user a
                 inner join university_test_paper_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 inner join university_project f on d.activity_id = f.id
                 inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.record_type = 1
          and b.read_over_status = 0
          and d.task_type = 1
          and e.is_valid = 1
          and e.teach_tool = 5
          and f.project_status = 1
          and a.sys_user_id = #{dto.userId}
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.keywords != null and dto.keywords != ''">
            AND  e.task_name  like  concat('%',#{dto.keywords},'%')
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

    </select>
    <select id="findAllReadOverExamList" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">

        select
            distinct
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            a.read_over_status  AS recordStatus
        from university_read_over_spot_check a
                 inner join university_test_paper_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 inner join university_course f on d.activity_id = f.id
                inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.read_over_type = 1
          and b.read_over_status = 2
          and d.task_type = 0
          and e.is_valid = 1
          and e.teach_tool = 5
          and f.course_type = 1
          and a.read_over_user = #{dto.userId}
          and a.read_over_status in (2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.keywords != null and dto.keywords != ''">
            AND  e.task_name  like  concat('%',#{dto.keywords},'%')
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        UNION ALL
        select
            distinct
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            a.read_over_status  AS recordStatus
        from university_read_over_spot_check a
                 inner join university_test_paper_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 inner join university_project f on d.activity_id = f.id
                inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.read_over_type = 1
          and b.read_over_status = 2
          and d.task_type = 1
          and e.is_valid = 1
          and e.teach_tool = 5
          and f.project_status = 1
          and a.read_over_user = #{dto.userId}
          and a.read_over_status in (2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.keywords != null and dto.keywords != ''">
            AND  e.task_name  like  concat('%',#{dto.keywords},'%')
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
    </select>
    <select id="findUnReadOverHomeworkList" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        select
            distinct
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            0  AS recordStatus
        from university_read_over_spot_check_user a
                 inner join university_homework_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 inner join university_course f on d.activity_id = f.id
                inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.record_type = 1
          and b.read_over_status = 0
          and d.task_type = 0
          and e.is_valid = 1
          and e.teach_tool = 6
          and f.course_type = 1
          and a.sys_user_id = #{dto.userId}
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.keywords != null and dto.keywords != ''">
            AND  e.task_name  like  concat('%',#{dto.keywords},'%')
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL
        select
            distinct
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            0  AS recordStatus
        from university_read_over_spot_check_user a
                 inner join university_homework_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 inner join university_project f on d.activity_id = f.id
                inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.record_type = 1
          and b.read_over_status = 0
          and d.task_type = 1
          and e.is_valid = 1
          and e.teach_tool = 6
          and f.project_status = 1
          and a.sys_user_id = #{dto.userId}
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.keywords != null and dto.keywords != ''">
            AND  e.task_name  like  concat('%',#{dto.keywords},'%')
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

    </select>
    <select id="findAllReadOverHomeworkList" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        select
            distinct
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            a.read_over_status  AS recordStatus
        from university_read_over_spot_check a
                 inner join university_homework_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 inner join university_course f on d.activity_id = f.id
                inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.read_over_type = 2
          and b.read_over_status = 2
          and d.task_type = 0
          and e.is_valid = 1
          and e.teach_tool = 6
          and f.course_type = 1
          and a.read_over_user = #{dto.userId}
          and a.read_over_status in (1,2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.keywords != null and dto.keywords != ''">
            AND  e.task_name  like  concat('%',#{dto.keywords},'%')
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL

        select
            distinct
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            a.read_over_status  AS recordStatus
        from university_read_over_spot_check a
                 inner join university_homework_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 inner join university_project f on d.activity_id = f.id
                inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.read_over_type = 2
          and b.read_over_status = 2
          and d.task_type = 1
          and e.is_valid = 1
          and e.teach_tool = 6
          and f.project_status = 1
          and a.read_over_user = #{dto.userId}
          and a.read_over_status in (2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.keywords != null and dto.keywords != ''">
            AND  e.task_name  like  concat('%',#{dto.keywords},'%')
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>


    </select>


</mapper>
