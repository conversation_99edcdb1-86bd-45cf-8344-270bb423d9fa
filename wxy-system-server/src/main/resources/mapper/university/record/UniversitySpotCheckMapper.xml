<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wxy.system.server.dao.university.project.UniversitySpotCheckDao">

    <sql id="AllColumns">
        a.id AS id,
        a.record_id AS recordId,
        a.read_over_id AS readOverId,
        a.spot_check_type AS spotCheckType,
        a.spot_check_user AS spotCheckUser,
        a.spot_check_user_name AS spotCheckrUserName,
        a.spot_check_time AS spotCheckTime,
        a.spot_check_comment AS spotCheckComment,
        a.spot_check_status AS spotCheckStatus,
        a.is_valid AS isValid,
        a.create_user AS createUser,
        a.create_time AS createTime,
        a.update_user AS updateUser,
        a.update_time AS updateTime

    </sql>
    <select id="getById" resultType="com.wxy.system.server.po.university.record.UniversitySpotCheck">
		SELECT
			<include refid="AllColumns"/>
		FROM university_spot_check a
		WHERE a.id = #{id} and a.is_valid = 1
	</select>

    <select id="findPage" resultType="com.wxy.system.server.po.university.record.UniversitySpotCheck" parameterType="com.wxy.system.common.dto.university.record.spotcheck.UniversitySpotCheckFindDTO">
		SELECT
			<include refid="AllColumns"/>
		FROM university_spot_check a
		<where>
			1=1 and a.is_valid = 1
		<if test="dto.keywords != null and dto.keywords != ''">
              and (a.record_id like  concat('%',#{dto.keywords},'%') or a.read_over_id like  concat('%',#{dto.keywords},'%')   )
        </if>
		</where>
	</select>
    <select id="findListByRecordId" resultType="com.wxy.system.common.vo.university.record.UniversitySpotCheckVo">
        SELECT
            a.id AS id,
            a.record_id AS recordId,
            a.read_over_id AS readOverId,
            a.spot_check_type AS spotCheckType,
            a.spot_check_user AS spotCheckUser,
            a.spot_check_user_name AS spotCheckUserName,
            a.spot_check_time AS spotCheckTime,
            a.spot_check_comment AS spotCheckComment,
            a.spot_check_status AS spotCheckStatus
        FROM university_spot_check a
        WHERE a.record_id = #{recordId} and a.is_valid = 1
        order by a.create_time desc
    </select>
    <select id="findByRecordId"
            resultType="com.wxy.system.common.vo.university.task.UniversityHomeworkUserSubmitDetSpotVo">
        SELECT
            ch.id,
            ch.spot_check_user,
            ch.spot_check_user_name,
            ch.spot_check_time,
            ch.spot_check_status,
            ch.spot_check_comment
        FROM
            university_spot_check ch
        WHERE
            ch.is_valid
          AND ch.record_id = #{id}
    </select>

    <select id="findUnSpotCheckExamList" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            null  AS projectScheduling,
            0  AS recordStatus
        from university_read_over_spot_check_user a
                 inner join university_test_paper_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 inner join university_course f on d.activity_id = f.id
                 left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                 inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.record_type = 2
          and b.read_over_status = 2
          and d.task_type = 0
          and e.is_valid = 1
          and e.teach_tool = 5
          and f.course_type = 1
          and a.sys_user_id = #{dto.userId}
          and g.spot_check_user is null
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            f.project_scheduling  AS projectScheduling,
            0  AS recordStatus
        from university_read_over_spot_check_user a
                 inner join university_test_paper_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 inner join university_project f on d.activity_id = f.id
                 left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.record_type = 2
          and b.read_over_status = 2
          and d.task_type = 1
          and e.is_valid = 1
          and e.teach_tool = 5
          and f.project_status = 1
          and a.sys_user_id = #{dto.userId}
          and g.spot_check_user is null
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

    </select>
    <select id="findOverSpotCheckExamList" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            null  AS projectScheduling,
            g.spot_check_status  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_test_paper_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            inner join university_course f on d.activity_id = f.id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 0
            and e.is_valid = 1
            and e.teach_tool = 5
            and f.course_type = 1
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user = #{dto.userId}
            and g.spot_check_status in(2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            f.project_scheduling  AS projectScheduling,
            g.spot_check_status  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_test_paper_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            inner join university_project f on d.activity_id = f.id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 1
            and e.is_valid = 1
            and e.teach_tool = 5
            and f.project_status = 1
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user= #{dto.userId}
            and g.spot_check_status in(2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

    </select>
    <select id="findUnSpotCheckHomeworkList" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            null  AS projectScheduling,
            0  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_homework_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            inner join university_course f on d.activity_id = f.id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 0
            and e.is_valid = 1
            and e.teach_tool = 6
            and f.course_type = 1
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user is null
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            f.project_scheduling  AS projectScheduling,
            0  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_homework_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            inner join university_project f on d.activity_id = f.id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 1
            and e.is_valid = 1
            and e.teach_tool = 6
            and f.project_status = 1
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user is null
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

    </select>
    <select id="findOverSpotCheckHomeworkList" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            null  AS projectScheduling,
            g.spot_check_status  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_homework_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            inner join university_course f on d.activity_id = f.id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 0
            and e.is_valid = 1
            and e.teach_tool = 6
            and f.course_type = 1
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user = #{dto.userId}
            and g.spot_check_status in(2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            f.project_scheduling  AS projectScheduling,
            g.spot_check_status  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_homework_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            inner join university_project f on d.activity_id = f.id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 1
            and e.is_valid = 1
            and e.teach_tool = 6
            and f.project_status = 1
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user= #{dto.userId}
            and g.spot_check_status in(2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

    </select>

    <!-- 分页查询待完成抽检数据 -->
    <select id="findUnfinishedCollectPage" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        <!-- 考试-未审批列表 -->
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            null  AS projectScheduling,
            0  AS recordStatus
        from university_read_over_spot_check_user a
                 inner join university_test_paper_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.record_type = 2
          and b.read_over_status = 2
          and d.task_type = 0
          and e.is_valid = 1
          and e.teach_tool = 5
          and a.sys_user_id = #{dto.userId}
          and g.spot_check_user is null
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL

        <!-- 考试-项目未审批列表 -->
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            f.project_scheduling  AS projectScheduling,
            0  AS recordStatus
        from university_read_over_spot_check_user a
                 inner join university_test_paper_record b on a.record_id = b.id
                 inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                 inner join university_course_participant_task d on c.participant_task_id = d.id
                 inner join university_task e on e.id = c.detail_id
                 inner join university_project f on d.activity_id = f.id
                 left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
          and c.is_valid = 1
          and d.is_valid = 1
          and a.record_type = 2
          and b.read_over_status = 2
          and d.task_type = 1
          and e.is_valid = 1
          and e.teach_tool = 5
          and f.project_status = 1
          and a.sys_user_id = #{dto.userId}
          and g.spot_check_user is null
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL

        <!-- 作业-未审批列表 -->
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            null  AS projectScheduling,
            0  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_homework_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 0
            and e.is_valid = 1
            and e.teach_tool = 6
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user is null
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL

        <!-- 作业-项目未审批列表 -->
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            f.project_scheduling  AS projectScheduling,
            0  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_homework_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            inner join university_project f on d.activity_id = f.id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 1
            and e.is_valid = 1
            and e.teach_tool = 6
            and f.project_status = 1
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user is null
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        ORDER BY recordTime DESC
    </select>

    <!-- 分页查询已完成抽检数据 -->
    <select id="findFinishedCollectPage" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        <!-- 考试-已审批列表 -->
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            null  AS projectScheduling,
            g.spot_check_status  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_test_paper_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 0
            and e.is_valid = 1
            and e.teach_tool = 5
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user= #{dto.userId}
            and g.spot_check_status in(2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL

        <!-- 考试-项目已审批列表 -->
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.exam_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            f.project_scheduling  AS projectScheduling,
            g.spot_check_status  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_test_paper_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            inner join university_project f on d.activity_id = f.id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 1
            and e.is_valid = 1
            and e.teach_tool = 5
            and f.project_status = 1
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user= #{dto.userId}
            and g.spot_check_status in(2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL

        <!-- 作业-已审批列表 -->
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            null  AS projectScheduling,
            g.spot_check_status  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_homework_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 0
            and e.is_valid = 1
            and e.teach_tool = 6
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user= #{dto.userId}
            and g.spot_check_status in(2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        UNION ALL

        <!-- 作业-项目已审批列表 -->
        select
            distinct
            d.activity_id AS activityId,
            b.id AS recordId,
            e.teach_tool AS  teachTool,
            e.id AS taskId,
            e.task_name AS taskName,
            d.sys_user_id AS userId,
            b.homework_end_time AS recordTime,
            c.task_type AS taskType,
            c.loop_date AS loopDate,
            f.project_scheduling  AS projectScheduling,
            g.spot_check_status  AS recordStatus
        from university_read_over_spot_check_user a
            inner join university_homework_record b on a.record_id = b.id
            inner join university_course_participant_task_det c on b.user_task_det_id = c.id
            inner join university_course_participant_task d on c.participant_task_id = d.id
            inner join university_task e on e.id = c.detail_id
            inner join university_project f on d.activity_id = f.id
            left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
            inner join sys_user u on u.id =d.sys_user_id
        where b.is_valid = 1
            and c.is_valid = 1
            and d.is_valid = 1
            and a.record_type = 2
            and d.task_type = 1
            and e.is_valid = 1
            and e.teach_tool = 6
            and f.project_status = 1
            and a.sys_user_id = #{dto.userId}
            and g.spot_check_user= #{dto.userId}
            and g.spot_check_status in(2,3)
        <if test="dto.taskId != null and dto.taskId != ''">
            AND  e.id = #{dto.taskId}
        </if>
        <if test="dto.loopDate != null and dto.loopDate != ''">
            AND  c.loop_date = #{dto.loopDate}
        </if>
        <if test="dto.activityId != null and dto.activityId != ''">
            AND   d.activity_id = #{dto.activityId}
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
            and u.sys_department_id in
            <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
            and d.sys_user_id in
            <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        ORDER BY recordTime DESC
    </select>

    <!-- 去重查询待完成抽检数据 -->
    <select id="findUnfinishedCollectPageWithDeduplication" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        SELECT * FROM (
            SELECT
                activityId,
                recordId,
                teachTool,
                taskId,
                taskName,
                userId,
                recordTime,
                taskType,
                loopDate,
                projectScheduling,
                recordStatus,
                ROW_NUMBER() OVER (PARTITION BY activityId, taskId ORDER BY recordTime DESC) as rn
            FROM (
                <!-- 考试-未审批列表 -->
                select
                    distinct
                    d.activity_id AS activityId,
                    b.id AS recordId,
                    e.teach_tool AS  teachTool,
                    e.id AS taskId,
                    e.task_name AS taskName,
                    d.sys_user_id AS userId,
                    b.exam_end_time AS recordTime,
                    c.task_type AS taskType,
                    c.loop_date AS loopDate,
                    null  AS projectScheduling,
                    0  AS recordStatus
                from university_read_over_spot_check_user a
                         inner join university_test_paper_record b on a.record_id = b.id
                         inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                         inner join university_course_participant_task d on c.participant_task_id = d.id
                         inner join university_task e on e.id = c.detail_id
                         left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                        inner join sys_user u on u.id =d.sys_user_id
                where b.is_valid = 1
                  and c.is_valid = 1
                  and d.is_valid = 1
                  and a.record_type = 2
                  and b.read_over_status = 2
                  and d.task_type = 0
                  and e.is_valid = 1
                  and e.teach_tool = 5
                  and a.sys_user_id = #{dto.userId}
                  and g.spot_check_user is null
                <if test="dto.taskId != null and dto.taskId != ''">
                    AND  e.id = #{dto.taskId}
                </if>
                <if test="dto.loopDate != null and dto.loopDate != ''">
                    AND  c.loop_date = #{dto.loopDate}
                </if>
                <if test="dto.activityId != null and dto.activityId != ''">
                    AND   d.activity_id = #{dto.activityId}
                </if>
                <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
                    and u.sys_department_id in
                    <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
                    and d.sys_user_id in
                    <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                </if>

                UNION ALL

                <!-- 考试-项目未审批列表 -->
                select
                    distinct
                    d.activity_id AS activityId,
                    b.id AS recordId,
                    e.teach_tool AS  teachTool,
                    e.id AS taskId,
                    e.task_name AS taskName,
                    d.sys_user_id AS userId,
                    b.exam_end_time AS recordTime,
                    c.task_type AS taskType,
                    c.loop_date AS loopDate,
                    f.project_scheduling  AS projectScheduling,
                    0  AS recordStatus
                from university_read_over_spot_check_user a
                         inner join university_test_paper_record b on a.record_id = b.id
                         inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                         inner join university_course_participant_task d on c.participant_task_id = d.id
                         inner join university_task e on e.id = c.detail_id
                         inner join university_project f on d.activity_id = f.id
                         left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                        inner join sys_user u on u.id =d.sys_user_id
                where b.is_valid = 1
                  and c.is_valid = 1
                  and d.is_valid = 1
                  and a.record_type = 2
                  and b.read_over_status = 2
                  and d.task_type = 1
                  and e.is_valid = 1
                  and e.teach_tool = 5
                  and f.project_status = 1
                  and a.sys_user_id = #{dto.userId}
                  and g.spot_check_user is null
                <if test="dto.taskId != null and dto.taskId != ''">
                    AND  e.id = #{dto.taskId}
                </if>
                <if test="dto.loopDate != null and dto.loopDate != ''">
                    AND  c.loop_date = #{dto.loopDate}
                </if>
                <if test="dto.activityId != null and dto.activityId != ''">
                    AND   d.activity_id = #{dto.activityId}
                </if>
                <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
                    and u.sys_department_id in
                    <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
                    and d.sys_user_id in
                    <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                </if>

                UNION ALL

                <!-- 作业-未审批列表 -->
                select
                    distinct
                    d.activity_id AS activityId,
                    b.id AS recordId,
                    e.teach_tool AS  teachTool,
                    e.id AS taskId,
                    e.task_name AS taskName,
                    d.sys_user_id AS userId,
                    b.homework_end_time AS recordTime,
                    c.task_type AS taskType,
                    c.loop_date AS loopDate,
                    null  AS projectScheduling,
                    0  AS recordStatus
                from university_read_over_spot_check_user a
                    inner join university_homework_record b on a.record_id = b.id
                    inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                    inner join university_course_participant_task d on c.participant_task_id = d.id
                    inner join university_task e on e.id = c.detail_id
                    left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                    inner join sys_user u on u.id =d.sys_user_id
                where b.is_valid = 1
                    and c.is_valid = 1
                    and d.is_valid = 1
                    and a.record_type = 2
                    and d.task_type = 0
                    and e.is_valid = 1
                    and e.teach_tool = 6
                    and a.sys_user_id = #{dto.userId}
                    and g.spot_check_user is null
                <if test="dto.taskId != null and dto.taskId != ''">
                    AND  e.id = #{dto.taskId}
                </if>
                <if test="dto.loopDate != null and dto.loopDate != ''">
                    AND  c.loop_date = #{dto.loopDate}
                </if>
                <if test="dto.activityId != null and dto.activityId != ''">
                    AND   d.activity_id = #{dto.activityId}
                </if>
                <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
                    and u.sys_department_id in
                    <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
                    and d.sys_user_id in
                    <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                </if>

                UNION ALL

                <!-- 作业-项目未审批列表 -->
                select
                    distinct
                    d.activity_id AS activityId,
                    b.id AS recordId,
                    e.teach_tool AS  teachTool,
                    e.id AS taskId,
                    e.task_name AS taskName,
                    d.sys_user_id AS userId,
                    b.homework_end_time AS recordTime,
                    c.task_type AS taskType,
                    c.loop_date AS loopDate,
                    f.project_scheduling  AS projectScheduling,
                    0  AS recordStatus
                from university_read_over_spot_check_user a
                    inner join university_homework_record b on a.record_id = b.id
                    inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                    inner join university_course_participant_task d on c.participant_task_id = d.id
                    inner join university_task e on e.id = c.detail_id
                    inner join university_project f on d.activity_id = f.id
                    left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                    inner join sys_user u on u.id =d.sys_user_id
                where b.is_valid = 1
                    and c.is_valid = 1
                    and d.is_valid = 1
                    and a.record_type = 2
                    and d.task_type = 1
                    and e.is_valid = 1
                    and e.teach_tool = 6
                    and f.project_status = 1
                    and a.sys_user_id = #{dto.userId}
                    and g.spot_check_user is null
                <if test="dto.taskId != null and dto.taskId != ''">
                    AND  e.id = #{dto.taskId}
                </if>
                <if test="dto.loopDate != null and dto.loopDate != ''">
                    AND  c.loop_date = #{dto.loopDate}
                </if>
                <if test="dto.activityId != null and dto.activityId != ''">
                    AND   d.activity_id = #{dto.activityId}
                </if>
                <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
                    and u.sys_department_id in
                    <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
                    and d.sys_user_id in
                    <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                </if>
            ) sub_query
        ) ranked_query
        WHERE rn = 1
        ORDER BY recordTime DESC
        LIMIT 6
    </select>

    <!-- 去重查询已完成抽检数据 -->
    <select id="findFinishedCollectPageWithDeduplication" resultType="com.wxy.system.common.vo.university.record.UniversityReadOverVo">
        SELECT * FROM (
            SELECT
                activityId,
                recordId,
                teachTool,
                taskId,
                taskName,
                userId,
                recordTime,
                taskType,
                loopDate,
                projectScheduling,
                recordStatus,
                ROW_NUMBER() OVER (PARTITION BY activityId, taskId ORDER BY recordTime DESC) as rn
            FROM (
                <!-- 考试-已审批列表 -->
                select
                    distinct
                    d.activity_id AS activityId,
                    b.id AS recordId,
                    e.teach_tool AS  teachTool,
                    e.id AS taskId,
                    e.task_name AS taskName,
                    d.sys_user_id AS userId,
                    b.exam_end_time AS recordTime,
                    c.task_type AS taskType,
                    c.loop_date AS loopDate,
                    null  AS projectScheduling,
                    g.spot_check_status  AS recordStatus
                from university_read_over_spot_check_user a
                    inner join university_test_paper_record b on a.record_id = b.id
                    inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                    inner join university_course_participant_task d on c.participant_task_id = d.id
                    inner join university_task e on e.id = c.detail_id
                    left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                    inner join sys_user u on u.id =d.sys_user_id
                where b.is_valid = 1
                    and c.is_valid = 1
                    and d.is_valid = 1
                    and a.record_type = 2
                    and d.task_type = 0
                    and e.is_valid = 1
                    and e.teach_tool = 5
                    and a.sys_user_id = #{dto.userId}
                    and g.spot_check_user= #{dto.userId}
                    and g.spot_check_status in(2,3)
                <if test="dto.taskId != null and dto.taskId != ''">
                    AND  e.id = #{dto.taskId}
                </if>
                <if test="dto.loopDate != null and dto.loopDate != ''">
                    AND  c.loop_date = #{dto.loopDate}
                </if>
                <if test="dto.activityId != null and dto.activityId != ''">
                    AND   d.activity_id = #{dto.activityId}
                </if>
                <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
                    and u.sys_department_id in
                    <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
                    and d.sys_user_id in
                    <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                </if>

                UNION ALL

                <!-- 考试-项目已审批列表 -->
                select
                    distinct
                    d.activity_id AS activityId,
                    b.id AS recordId,
                    e.teach_tool AS  teachTool,
                    e.id AS taskId,
                    e.task_name AS taskName,
                    d.sys_user_id AS userId,
                    b.exam_end_time AS recordTime,
                    c.task_type AS taskType,
                    c.loop_date AS loopDate,
                    f.project_scheduling  AS projectScheduling,
                    g.spot_check_status  AS recordStatus
                from university_read_over_spot_check_user a
                    inner join university_test_paper_record b on a.record_id = b.id
                    inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                    inner join university_course_participant_task d on c.participant_task_id = d.id
                    inner join university_task e on e.id = c.detail_id
                    inner join university_project f on d.activity_id = f.id
                    left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                    inner join sys_user u on u.id =d.sys_user_id
                where b.is_valid = 1
                    and c.is_valid = 1
                    and d.is_valid = 1
                    and a.record_type = 2
                    and d.task_type = 1
                    and e.is_valid = 1
                    and e.teach_tool = 5
                    and f.project_status = 1
                    and a.sys_user_id = #{dto.userId}
                    and g.spot_check_user= #{dto.userId}
                    and g.spot_check_status in(2,3)
                <if test="dto.taskId != null and dto.taskId != ''">
                    AND  e.id = #{dto.taskId}
                </if>
                <if test="dto.loopDate != null and dto.loopDate != ''">
                    AND  c.loop_date = #{dto.loopDate}
                </if>
                <if test="dto.activityId != null and dto.activityId != ''">
                    AND   d.activity_id = #{dto.activityId}
                </if>
                <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
                    and u.sys_department_id in
                    <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
                    and d.sys_user_id in
                    <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                </if>

                UNION ALL

                <!-- 作业-已审批列表 -->
                select
                    distinct
                    d.activity_id AS activityId,
                    b.id AS recordId,
                    e.teach_tool AS  teachTool,
                    e.id AS taskId,
                    e.task_name AS taskName,
                    d.sys_user_id AS userId,
                    b.homework_end_time AS recordTime,
                    c.task_type AS taskType,
                    c.loop_date AS loopDate,
                    null  AS projectScheduling,
                    g.spot_check_status  AS recordStatus
                from university_read_over_spot_check_user a
                    inner join university_homework_record b on a.record_id = b.id
                    inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                    inner join university_course_participant_task d on c.participant_task_id = d.id
                    inner join university_task e on e.id = c.detail_id
                    left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                    inner join sys_user u on u.id =d.sys_user_id
                where b.is_valid = 1
                    and c.is_valid = 1
                    and d.is_valid = 1
                    and a.record_type = 2
                    and d.task_type = 0
                    and e.is_valid = 1
                    and e.teach_tool = 6
                    and a.sys_user_id = #{dto.userId}
                    and g.spot_check_user= #{dto.userId}
                    and g.spot_check_status in(2,3)
                <if test="dto.taskId != null and dto.taskId != ''">
                    AND  e.id = #{dto.taskId}
                </if>
                <if test="dto.loopDate != null and dto.loopDate != ''">
                    AND  c.loop_date = #{dto.loopDate}
                </if>
                <if test="dto.activityId != null and dto.activityId != ''">
                    AND   d.activity_id = #{dto.activityId}
                </if>
                <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
                    and u.sys_department_id in
                    <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
                    and d.sys_user_id in
                    <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                </if>

                UNION ALL

                <!-- 作业-项目已审批列表 -->
                select
                    distinct
                    d.activity_id AS activityId,
                    b.id AS recordId,
                    e.teach_tool AS  teachTool,
                    e.id AS taskId,
                    e.task_name AS taskName,
                    d.sys_user_id AS userId,
                    b.homework_end_time AS recordTime,
                    c.task_type AS taskType,
                    c.loop_date AS loopDate,
                    f.project_scheduling  AS projectScheduling,
                    g.spot_check_status  AS recordStatus
                from university_read_over_spot_check_user a
                    inner join university_homework_record b on a.record_id = b.id
                    inner join university_course_participant_task_det c on b.user_task_det_id = c.id
                    inner join university_course_participant_task d on c.participant_task_id = d.id
                    inner join university_task e on e.id = c.detail_id
                    inner join university_project f on d.activity_id = f.id
                    left  join university_spot_check g on b.id = g.record_id and g.spot_check_user = a.sys_user_id
                    inner join sys_user u on u.id =d.sys_user_id
                where b.is_valid = 1
                    and c.is_valid = 1
                    and d.is_valid = 1
                    and a.record_type = 2
                    and d.task_type = 1
                    and e.is_valid = 1
                    and e.teach_tool = 6
                    and f.project_status = 1
                    and a.sys_user_id = #{dto.userId}
                    and g.spot_check_user= #{dto.userId}
                    and g.spot_check_status in(2,3)
                <if test="dto.taskId != null and dto.taskId != ''">
                    AND  e.id = #{dto.taskId}
                </if>
                <if test="dto.loopDate != null and dto.loopDate != ''">
                    AND  c.loop_date = #{dto.loopDate}
                </if>
                <if test="dto.activityId != null and dto.activityId != ''">
                    AND   d.activity_id = #{dto.activityId}
                </if>
                <if test="dto.deptIdList !=null and dto.deptIdList.size()>0 ">
                    and u.sys_department_id in
                    <foreach collection="dto.deptIdList" item="deptId" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="dto.userIdList !=null and dto.userIdList.size()>0 ">
                    and d.sys_user_id in
                    <foreach collection="dto.userIdList" item="userId" open="(" close=")" separator=",">
                        #{userId}
                    </foreach>
                </if>
            ) sub_query
        ) ranked_query
        WHERE rn = 1
        ORDER BY recordTime DESC
        LIMIT 6
    </select>


</mapper>
