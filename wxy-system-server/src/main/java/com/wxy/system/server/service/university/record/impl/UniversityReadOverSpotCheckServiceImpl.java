package com.wxy.system.server.service.university.record.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wxy.base.exception.AppBizException;
import com.wxy.base.util.Common;
import com.wxy.base.util.FeignUtils;
import com.wxy.base.util.StringUtils;
import com.wxy.redis.annotation.RedisLock;
import com.wxy.shop.common.dto.base.QueryShopBaseInfoListDTO;
import com.wxy.shop.common.vo.base.QueryShopBaseInfoListVO;
import com.wxy.shop.feign.client.base.ShopBaseClient;
import com.wxy.system.common.dto.university.message.UniversityMessageDTO;
import com.wxy.system.common.dto.university.record.UniversityQuestionReadOverSaveDTO;
import com.wxy.system.common.dto.university.record.UniversityReadOverSpotCheckFileSaveDTO;
import com.wxy.system.common.dto.university.record.UniversityReadOverSpotCheckFindDTO;
import com.wxy.system.common.dto.university.record.UniversityTeachAllRecordDetDTO;
import com.wxy.system.common.dto.university.record.readover.*;
import com.wxy.system.common.enumerate.SysHttpResult;
import com.wxy.system.common.enumerate.university.MessageTypeEnum;
import com.wxy.system.common.vo.exam.SysExamQuestionCollectVo;
import com.wxy.system.common.vo.exam.SysRelExamQuestionVo;
import com.wxy.system.common.vo.exam.SysStudyProfileDetailVo;
import com.wxy.system.common.vo.university.course.UniversityCourseVO;
import com.wxy.system.common.vo.university.project.UniversityProjectVo;
import com.wxy.system.common.vo.university.record.*;
import com.wxy.system.common.vo.university.record.eaxm.UniversityExamReadOverAllVo;
import com.wxy.system.common.vo.university.record.eaxm.UniversityExamReadOverCollectVo;
import com.wxy.system.common.vo.university.record.eaxm.UniversityTestPaperRecordAllVo;
import com.wxy.system.common.vo.university.record.eaxm.UniversityTestPaperRecordVo;
import com.wxy.system.common.vo.university.record.homework.UniversityHomeworkReadOverAllCollectVo;
import com.wxy.system.common.vo.university.record.homework.UniversityHomeworkReadOverCollectVo;
import com.wxy.system.common.vo.university.record.homework.UniversityHomeworkRecordAllVo;
import com.wxy.system.common.vo.university.record.homework.UniversityHomeworkRecordVo;
import com.wxy.system.common.vo.university.task.UniversityHomeworkSubmitQuestionVo;
import com.wxy.system.common.vo.university.task.UniversityTaskExamVo;
import com.wxy.system.common.vo.university.task.UniversityTaskHomeworkVo;
import com.wxy.system.common.vo.university.task.UniversityTaskVO;
import com.wxy.system.common.vo.university.userdet.UniversityUserTaskDetUpdateVO;
import com.wxy.system.common.vo.user.QueryUserListVO;
import com.wxy.system.server.cvonvert.university.record.ExamRecordConvert;
import com.wxy.system.server.dao.university.record.UniversityReadOverSpotCheckDao;
import com.wxy.system.server.po.university.course.UniversityCourse;
import com.wxy.system.server.po.university.project.UniversityProject;
import com.wxy.system.server.po.university.record.UniversityHomeworkQuestion;
import com.wxy.system.server.po.university.record.UniversityQuestionReadOver;
import com.wxy.system.server.po.university.record.UniversityReadOverSpotCheck;
import com.wxy.system.server.po.university.record.UniversityReadOverSpotCheckFile;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTask;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTaskDet;
import com.wxy.system.server.service.study.profile.ISysStudyProfileService;
import com.wxy.system.server.service.university.course.IUniversityCourseService;
import com.wxy.system.server.service.university.message.IUniversityMessageRecordService;
import com.wxy.system.server.service.university.project.IUniversityProjectService;
import com.wxy.system.server.service.university.record.*;
import com.wxy.system.server.service.university.students.IUniversityUserTaskDetService;
import com.wxy.system.server.service.university.students.IUniversityUserTaskService;
import com.wxy.system.server.service.university.task.IUniversityTaskService;
import com.wxy.system.server.service.user.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 企业大学-批阅信息和抽检表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-20 17:06:11
 */

@Slf4j
@Service
public class UniversityReadOverSpotCheckServiceImpl extends ServiceImpl<UniversityReadOverSpotCheckDao, UniversityReadOverSpotCheck> implements IUniversityReadOverSpotCheckService   {
    @Resource
    private ISysStudyProfileService studyProfileService;

    @Resource
    private IUniversityCourseService universityCourseService;

    @Resource
    private IUniversityProjectService universityProjectService;

    @Resource
    private IUniversityTaskService universityTaskService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private ExamRecordConvert examRecordConvert;

    @Resource
    private IUniversityQuestionReadOverService universityQuestionReadOverService;

    @Lazy
    @Autowired(required = false)
    private IUniversityReadOverSpotCheckFileService universityReadOverSpotCheckFileService;

    @Resource
    private IUniversityTestPaperRecordService universityTestPaperRecordService;

    @Resource
    private IUniversityHomeworkRecordService universityHomeworkRecordService;

    @Resource
    private IUniversitySpotCheckService universitySpotCheckService;


    @Resource
    private IUniversityUserTaskDetService universityUserTaskDetService;


    @Autowired(required = false)
    private IUniversityReadOverSpotCheckUserService universityReadOverSpotCheckUserService;

    @Resource
    private IUniversityHomeworkQuestionService universityHomeworkQuestionService;

    @Resource
    private IUniversityMessageRecordService messageRecordService;

    @Resource
    private IUniversityUserTaskService userTaskService;

    @Resource
    private ShopBaseClient shopBaseClient;

    /**
     * 查询
     * @param id
     * @return
     */
    @Override
    public UniversityReadOverSpotCheck getById(String id){
        return getBaseMapper().getById(id);
    }

    /**
     * 分页查询
     * @param page
     * @param dto
     * @return
     */
    @Override
    public IPage<UniversityReadOverSpotCheck> findPage(IPage page, UniversityReadOverSpotCheckFindDTO dto){
        // 设置当前用户ID
        if (dto.getUserId() == null) {
            dto.setUserId(Common.getUserId());
        }

        // 门店转部门
        if (dto.getShopIdList() != null && dto.getShopIdList().size() > 0) {
            QueryShopBaseInfoListDTO queryShopBaseInfoListDTO = new QueryShopBaseInfoListDTO();
            queryShopBaseInfoListDTO.setShopIdList(dto.getShopIdList());
            List<QueryShopBaseInfoListVO> baseInfoListVOS = FeignUtils.processFeignResult(shopBaseClient.findList(queryShopBaseInfoListDTO));
            if (baseInfoListVOS != null && !baseInfoListVOS.isEmpty()) {
                dto.setDeptIdList(baseInfoListVOS.stream().map(QueryShopBaseInfoListVO::getSysDeptId).collect(Collectors.toList()));
            }
        }

        return getBaseMapper().findPage(page,dto);
    }

    /**
     * 删除
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(UniversityReadOverSpotCheck entity){
        this.getBaseMapper().deleteByIdWithFill(entity);
    }

     /**
     * 批量保存
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSave(List<UniversityReadOverSpotCheck> list){
        return this.saveBatch(list);
    }


    /**
     * 批量更新
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdate(List<UniversityReadOverSpotCheck> list){
        list.forEach(t->
                t.setUpdateTime(null)
                .setUpdateUser(null)
        );
        return this.updateBatchById(list);
    }

    @Override
    public UniversityTeachAllRecordVo teachRecordList() {
        UniversityTeachAllRecordVo vo = new UniversityTeachAllRecordVo();

        String userId = Common.getUserId();
        //获取教学-课程列表
        List<UniversityTeachRecordVo> courseLit = this.baseMapper.findCourseLit(userId,null);
        //获取教学-项目列表
        List<UniversityTeachRecordVo> projectList = this.baseMapper.findProjectList(userId,null);


        Map<String, Set<String>> userMap = new HashMap<>();
        Map<String, Set<String>> taskMap = new HashMap<>();
        Map<String,Integer> statusMap = new HashMap<>();
        Map<String, List<UniversityTeachRecordVo>> teachMap = courseLit.stream().collect(Collectors.groupingBy(UniversityTeachRecordVo::getTeachId));
        teachMap.putAll(projectList.stream().collect(Collectors.groupingBy(UniversityTeachRecordVo::getTeachId)));
        if (!courseLit.isEmpty()) {
            for (UniversityTeachRecordVo recordVo : courseLit) {
                //设置教学信息
                setTeachInfo(userMap, taskMap, statusMap, recordVo);
            }
        }
        if (!projectList.isEmpty()){
            for (UniversityTeachRecordVo recordVo : projectList) {
                //设置教学信息
                setTeachInfo(userMap, taskMap, statusMap, recordVo);
            }
        }

        List<UniversityTeachRecordVo> allList = new ArrayList<>();
        for (Map.Entry<String, List<UniversityTeachRecordVo>> en : teachMap.entrySet()) {
            List<UniversityTeachRecordVo> recordList = en.getValue();
            UniversityTeachRecordVo recordVo = recordList.get(0);
            recordVo.setSysUserId(null);
            recordVo.setTaskId(null);
            recordVo.setTaskStatus(null);

            //设置学员
            Set<String> userSet = userMap.get(recordVo.getTeachId());
            if (userSet != null && !userSet.isEmpty()) {
                recordVo.setStudentNum(userSet.size());
            } else {
                recordVo.setStudentNum(0);
            }

            //设置任务
            Set<String> taskSet = taskMap.get(recordVo.getTeachId());
            if (taskSet != null && !taskSet.isEmpty()) {
                recordVo.setTeachTaskNum(taskSet.size());
            } else {
                recordVo.setTeachTaskNum(0);
            }

            //设置教学状态
            Integer status = statusMap.get(recordVo.getTeachId());
            if(status!= null && status.equals(1)){
                recordVo.setTeachStaus(1);
            }else{
                recordVo.setTeachStaus(2);
            }

            allList.add(recordVo);
        }


        List<String> fileIdList = allList.stream().map(UniversityTeachRecordVo::getFileId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        if(!fileIdList.isEmpty()){
            //获取附件信息
            List<SysStudyProfileDetailVo> fileDetList = studyProfileService.findDetailByIds(fileIdList);
            //设置详情到附件关系表里
            Map<String, SysStudyProfileDetailVo> fileDetMap = fileDetList.stream()
                    .collect(Collectors.toMap(SysStudyProfileDetailVo::getId, t -> t, (k1, k2) -> k1));
            //设置附件
            for (UniversityTeachRecordVo recordVo : allList) {
                SysStudyProfileDetailVo fileDet = fileDetMap.get(recordVo.getFileId());
                if(fileDet!= null){
                    recordVo.setProfileDetailVo(fileDet);
                }
            }
        }

        //全部教学列表
        allList = allList.stream().sorted(Comparator.comparing(UniversityTeachRecordVo::getCreateTime))
                .distinct().collect(Collectors.toList());
        vo.setAllList(allList);

        //进行中-教学列表
        List<UniversityTeachRecordVo> ingList = allList.stream().filter(t->t.getTeachStaus().equals(1))
                .sorted(Comparator.comparing(UniversityTeachRecordVo::getCreateTime))
                .distinct().collect(Collectors.toList());
        //结束-教学列表
        List<UniversityTeachRecordVo> endList = allList.stream().filter(t->t.getTeachStaus().equals(2))
                .sorted(Comparator.comparing(UniversityTeachRecordVo::getCreateTime))
                .distinct().collect(Collectors.toList());

        vo.setIngList(ingList);
        vo.setEndList(endList);

        return vo;
    }



    /**
     * 设置教学信息
     * @param userMap
     * @param taskMap
     * @param statusMap
     * @param recordVo
     */
    private void setTeachInfo(Map<String, Set<String>> userMap, Map<String, Set<String>> taskMap, Map<String, Integer> statusMap, UniversityTeachRecordVo recordVo) {
        //设置学员
        Set<String> userSet = userMap.get(recordVo.getTeachId());
        if (userSet != null && !userSet.isEmpty()) {
            userSet.add(recordVo.getSysUserId());
        } else {
            userSet = new HashSet<>();
            userSet.add(recordVo.getSysUserId());
        }
        userMap.put(recordVo.getTeachId(), userSet);

        //设置任务
        Set<String> taskSet = taskMap.get(recordVo.getTeachId());
        if (taskSet != null && !taskSet.isEmpty()) {
            taskSet.add(recordVo.getTaskId());
        } else {
            taskSet = new HashSet<>();
            taskSet.add(recordVo.getTaskId());
        }
        taskMap.put(recordVo.getTeachId(), taskSet);

        //设置教学状态
        Integer status = statusMap.get(recordVo.getTeachId());
        // 状态 1-进行中;2-结束
        List<Integer> ingStatusList = List.of(0,1);
        if(status!= null && status.equals(1)){
            status = 1;
        } else if(recordVo.getTaskStatus() == null  || ingStatusList.contains(recordVo.getTaskStatus())){
            status = 1;
        }else{
            status = 2;
        }
        statusMap.put(recordVo.getTeachId(), status);
    }


    /**
     * 教学详情
     *
     * @param dto
     * @return
     */
    @Override
    public UniversityTeachAllRecordDetVo getTeachDet(UniversityTeachAllRecordDetDTO dto) {
        UniversityTeachAllRecordDetVo vo = new UniversityTeachAllRecordDetVo();
        vo.setTeachType(dto.getTeachType());
        String userId = Common.getUserId();

        if(dto.getTeachType().equals(1)){
            //课程信息
            UniversityCourseVO courseVo = universityCourseService.queryById(dto.getTeachId());
            vo.setCourseVo(courseVo);

            //获取教学-课程列表
            List<UniversityTeachRecordVo> courseLit = this.baseMapper.findCourseLit(userId,dto.getTeachId());
            if(courseLit.isEmpty()){
                return vo;
            }
            //任务id列表
            List<String> taskIdList = courseLit.stream().map(UniversityTeachRecordVo::getTaskId).distinct().collect(Collectors.toList());
            //学员id列表
            List<String> userIdList = courseLit.stream().map(UniversityTeachRecordVo::getSysUserId).distinct().collect(Collectors.toList());
            //用户信息列表
            List<TeachUserVO> userInfoList = sysUserService.findListByUserIds(userIdList);

            Map<String,List<UniversityTeachRecordVo>> taskUserMap = courseLit.stream()
                    .collect(Collectors.groupingBy(UniversityTeachRecordVo::getTaskId));
            Map<String,TeachUserVO> userMap = userInfoList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            if(taskIdList.isEmpty()){
                return vo;
            }
            //获取任务列表
            List<TeachTaskVO> taskList = universityTaskService.findListByTaskIds(taskIdList);
            for (TeachTaskVO taskVO : taskList) {
                List<UniversityTeachRecordVo> teachUserList = taskUserMap.get(taskVO.getId());
                //
                if (teachUserList == null ){
                    continue;
                }
                //设置用户信息列表
                List<TeachUserVO> teachUserVOList = new ArrayList<>();
                for (UniversityTeachRecordVo recordVo : teachUserList) {
                    TeachUserVO teachUserVO = new TeachUserVO();
                    TeachUserVO userVo = userMap.get(recordVo.getSysUserId());
                    BeanUtils.copyProperties(userVo,teachUserVO);
                    teachUserVO.setTaskId(taskVO.getId())
                            .setProgress(recordVo.getProgress());
                    teachUserVOList.add(teachUserVO);
                }
                //设置用户列表
                taskVO.setTaskUserList(teachUserVOList);
            }
            vo.setTaskList(taskList);
        }else{
            //项目信息
            UniversityProjectVo projectVo = universityProjectService.getById(dto.getTeachId());
            vo.setProjectVo(projectVo);

            //获取教学-项目列表
            List<UniversityTeachRecordVo> projectList = this.baseMapper.findProjectList(userId,dto.getTeachId());
            if(projectList.isEmpty()){
                return vo;
            }
            Map<String, UniversityTeachRecordVo> planMap = projectList.stream().collect(Collectors.toMap(UniversityTeachRecordVo::getCourseId, t -> t, (k1, k2) -> k1));


            //课程id列表
            List<String> courseIdList = projectList.stream().map(UniversityTeachRecordVo::getCourseId).distinct().collect(Collectors.toList());
            //获取课程列表
            List<TeachCourseVO> teachCourseList = universityCourseService.findListByCourseIds(courseIdList);
            if(teachCourseList.isEmpty()){
                return vo;
            }
            List<String> fileList = teachCourseList.stream().map(TeachCourseVO::getStudyProfileId).collect(Collectors.toList());
            Map<String, SysStudyProfileDetailVo> fileDetMap = new HashMap<>();
            if(!fileList.isEmpty()){
                //获取附件信息
                List<SysStudyProfileDetailVo> fileDetList = studyProfileService.findDetailByIds(fileList);
                //设置详情到附件关系表里
                fileDetMap = fileDetList.stream().collect(Collectors.toMap(SysStudyProfileDetailVo::getId, t -> t, (k1, k2) -> k1));
            }



            //任务id列表
            List<String> taskIdList = projectList.stream().map(UniversityTeachRecordVo::getTaskId).distinct().collect(Collectors.toList());
            //学员id列表
            List<String> userIdList = projectList.stream().map(UniversityTeachRecordVo::getSysUserId).distinct().collect(Collectors.toList());
            //用户信息列表
            List<TeachUserVO> userInfoList = sysUserService.findListByUserIds(userIdList);

            Map<String,List<UniversityTeachRecordVo>> taskUserMap = projectList.stream()
                    .collect(Collectors.groupingBy(UniversityTeachRecordVo::getTaskId));
            Map<String,TeachUserVO> userMap = userInfoList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            if(taskIdList.isEmpty()){
                return vo;
            }
            //获取任务列表
            List<TeachTaskVO> taskList = universityTaskService.findListByTaskIds(taskIdList);
            Map<String, List<TeachTaskVO>> taskMap = taskList.stream().collect(Collectors.groupingBy(t -> t.getCourseId()));

            for (TeachCourseVO courseVO : teachCourseList) {
                //设置文件
                SysStudyProfileDetailVo fileDet = fileDetMap.get(courseVO.getStudyProfileId());
                if(fileDet!= null){
                    courseVO.setStudyProfileVO(fileDet);
                }
                //获取课程下的任务列表
                List<TeachTaskVO> taskInfoList = taskMap.get(courseVO.getId());
                if(taskInfoList == null || taskInfoList.isEmpty()){
                    continue;
                }
                for (TeachTaskVO taskVO : taskInfoList) {
                    List<UniversityTeachRecordVo> teachUserList = taskUserMap.get(taskVO.getId());
                    //TeachUserVO userVo = userMap.get(taskVO.getId());
                    if (teachUserList == null ){
                        continue;
                    }
                    //设置用户信息列表
                    List<TeachUserVO> teachUserVOList = new ArrayList<>();
                    for (UniversityTeachRecordVo recordVo : teachUserList) {
                        TeachUserVO teachUserVO = new TeachUserVO();
                        TeachUserVO userVo = userMap.get(recordVo.getSysUserId());
                        BeanUtils.copyProperties(userVo,teachUserVO);
                        teachUserVO.setTaskId(taskVO.getId())
                                .setProgress(recordVo.getProgress());
                        teachUserVOList.add(teachUserVO);
                    }

                    //设置用户列表
                    taskVO.setTaskUserList(teachUserVOList);
                }
                courseVO.setTaskList(taskInfoList);
                UniversityTeachRecordVo recordVo = planMap.get(courseVO.getId());
                if(recordVo != null){
                    courseVO.setProjectPlanBeginDate(recordVo.getProjectPlanBeginDate());
                    courseVO.setProjectPlanEndDate(recordVo.getProjectPlanEndDate());
                    courseVO.setProjectPlanDayBegin(recordVo.getProjectPlanDayBegin());
                    courseVO.setProjectPlanDayEnd(recordVo.getProjectPlanDayEnd());
                }

            }

            //设置课程列表
            vo.setCourseList(teachCourseList);
        }
        return vo;
    }


    /**
     * 审批列表
     * @param dto
     * @return
     */
    @Override
    public UniversityReadOverAllVo findReadOverList(UniversityReadOverAllDTO dto) {
        UniversityReadOverAllVo vo = new UniversityReadOverAllVo();
        //获取全部列表
        List<UniversityReadOverVo> allList = this.baseMapper.findReadOverList(dto);
        allList = allList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime)).collect(Collectors.toList());
        vo.setAllList(allList);
        //考试列表
        List<UniversityReadOverVo> examList = allList.stream().filter(t->t.getTeachTool().equals(5)).collect(Collectors.toList());
        //作业列表
        List<UniversityReadOverVo> homeworkList = allList.stream().filter(t->t.getTeachTool().equals(6)).collect(Collectors.toList());
        vo.setExamList(examList);
        vo.setHomeworkList(homeworkList);
        return vo;
    }


    /**
     * 批阅保存
     *
     * @param dto
     * @param recordId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(param = "recordId")
    public void examReadOverSave(UniversityExamReadOverSaveDTO dto,String recordId) {
        //是否被批阅
        QueryWrapper<UniversityReadOverSpotCheck> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UniversityReadOverSpotCheck::getRecordId,dto.getRecordId());
        List<UniversityReadOverSpotCheck> list = getBaseMapper().selectList(queryWrapper);
        if(!list.isEmpty()){
            SysHttpResult.ERROR_UNIVERSITY_EXAM_IS_READ_OVER.appBizException();
        }

        //保存批阅主表
        UniversityReadOverSpotCheck entity = new UniversityReadOverSpotCheck();
        BeanUtils.copyProperties(dto,entity);
        entity.setReadOverTime(new Timestamp(System.currentTimeMillis()))
                .setReadOverUser(Common.getUserId())
                .setReadOverType(1);
        QueryUserListVO userInfo = sysUserService.getUserInfo(Common.getUserId());
        if(userInfo.getUserId()!= null){
            entity.setReadOverUserName(userInfo.getUserName());
        }
        if(dto.getQuestionReadOverList()!= null && !dto.getQuestionReadOverList().isEmpty()){
            BigDecimal readOverScore = BigDecimal.ZERO;
            for (UniversityQuestionReadOverSaveDTO readOverSaveDTO : dto.getQuestionReadOverList()) {
                if(readOverSaveDTO.getReadOverScore() == null){
                    continue;
                }
                readOverScore = readOverScore.add(readOverSaveDTO.getReadOverScore());
            }
            entity.setReadOverScore(readOverScore);
        }

        this.baseMapper.insert(entity);


        //问题列表保存
        if(dto.getQuestionReadOverList()!= null && !dto.getQuestionReadOverList().isEmpty()){
            List<UniversityQuestionReadOver> questionReadOverList = examRecordConvert.toUniversityQuestionReadOverList(dto.getQuestionReadOverList());
            questionReadOverList.forEach(t->t.setReadOverId(entity.getId()));
            universityQuestionReadOverService.batchSave(questionReadOverList);
        }

        //批阅文件批量保存
        List<UniversityReadOverSpotCheckFile> fileList = new ArrayList<>();
        if(dto.getFileList()!= null && !dto.getFileList().isEmpty()){
            for (UniversityReadOverSpotCheckFileSaveDTO fileSaveDTO : dto.getFileList()) {
                UniversityReadOverSpotCheckFile file = new UniversityReadOverSpotCheckFile();
                file.setRecordId(dto.getRecordId())
                        .setObjectType(1)
                        .setObjectId(entity.getId())
                        .setSysVideoMultimediaId(fileSaveDTO.getSysVideoMultimediaId())
                        .setReadOverSort(fileSaveDTO.getReadOverSort());
                fileList.add(file);
            }
            universityReadOverSpotCheckFileService.batchSave(fileList);

            //文件引用
            List<String> addFileIds = fileList.stream().map(t -> t.getSysVideoMultimediaId()).collect(Collectors.toList());
            if(!addFileIds.isEmpty()){
                studyProfileService.useProfile(addFileIds,entity.getId());
            }
        }


        //更新考试记录
        Boolean isPass = universityTestPaperRecordService.finshExam(dto);

        if(isPass){
            entity.setReadOverStatus(2);
        }else{
            entity.setReadOverStatus(3);
        }
        this.baseMapper.updateById(entity);


        UniversityTestPaperRecordVo record = universityTestPaperRecordService.getInfo(dto.getRecordId());
        //获取用户任务明细
        UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(record.getUserTaskDetId());
        //考试任务
        UniversityTaskVO task = universityTaskService.queryById(det.getDetailId());
        UniversityTaskExamVo taskExamVo = task.getTaskExam();
        //是否抽检(0-否;1-是;默认否)
        if(taskExamVo.getIsSpotCheck().equals(1)){
            //抽检
            universityReadOverSpotCheckUserService.saveUserIds(taskExamVo.getSpotCheckUserList(), 2,record.getId(), det, Common.getTenantId(), Common.getUserId());
        }


        // 批阅完成通知
        this.pushReadOverMessage(dto.getRecordId(), task, det, entity.getReadOverStatus());
    }

    /**
     * 作业批阅保存
     *
     * @param dto
     * @param recordId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(param = "recordId")
    public void homeworkReadOverSave(UniversityHomeworkReadOverSaveDTO dto, String recordId) {
        //是否被批阅
        QueryWrapper<UniversityReadOverSpotCheck> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UniversityReadOverSpotCheck::getRecordId,dto.getRecordId());
        List<UniversityReadOverSpotCheck> list = getBaseMapper().selectList(queryWrapper);
        if(!list.isEmpty()){
            SysHttpResult.ERROR_UNIVERSITY_HOMEWORK_IS_READ_OVER.appBizException();
        }

        //作业任务
        UniversityHomeworkRecordVo universityHomeworkRecordVo = universityHomeworkRecordService.get(dto.getRecordId());
        //获取用户任务
        UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(universityHomeworkRecordVo.getUserTaskDetId());
        //作业任务
        UniversityTaskVO task = universityTaskService.queryById(det.getDetailId());
        UniversityTaskHomeworkVo taskHomework = task.getTaskHomework();

        //检验
        if(taskHomework.getReadOverType()!= null){
            //批阅方式(1-直接批阅;2-评分批阅;)
            if(taskHomework.getReadOverType().equals(1)){
                if(!List.of(2,3).contains(dto.getReadOverStatus())){
                    throw new AppBizException("999000", "直接批阅的任务,直接选择通过或者不通过", StringUtils.EMPTY);
                }
            }else if(taskHomework.getReadOverType().equals(2)){
                if(!dto.getReadOverStatus().equals(1)){
                    throw new AppBizException("999000", "评分批阅的任务,只能直接提交", StringUtils.EMPTY);
                }
                if(dto.getReadOverScore() == null){
                    throw new AppBizException("999000", "评分批阅的任务,需要进行评分操作", StringUtils.EMPTY);
                }
                BigDecimal readOverScore = new BigDecimal(taskHomework.getReadOverScore());

                //批阅状态处理
                if(dto.getReadOverScore().compareTo(readOverScore)>=0){
                    dto.setReadOverStatus(2);
                }else{
                    dto.setReadOverStatus(3);
                }
            }
        }


        //题目更新处理
        List<UniversityHomeworkQuestion> questionList = universityHomeworkQuestionService.findListByReord(dto.getRecordId());
        questionList = questionList.stream().filter(t -> t.getQuestionType().equals(6)).collect(Collectors.toList());
        if(dto.getReadOverStatus().equals(2)){
            for (UniversityHomeworkQuestion question : questionList) {
                if(!question.getQuestionType().equals(6)){
                    continue;
                }
                question.setIsRight(1);
                question.setUpdateTime(null).setUpdateUser(null);
            }
        }else  if(dto.getReadOverStatus().equals(3)){
            for (UniversityHomeworkQuestion question : questionList) {
                if(!question.getQuestionType().equals(6)){
                    continue;
                }
                question.setIsRight(0);
                question.setUpdateTime(null).setUpdateUser(null);
            }
        }
        if(questionList!= null && !questionList.isEmpty()){
            universityHomeworkQuestionService.batchUpdate(questionList);
        }




        //保存批阅主表
        UniversityReadOverSpotCheck entity = new UniversityReadOverSpotCheck();
        BeanUtils.copyProperties(dto,entity);
        entity.setReadOverTime(new Timestamp(System.currentTimeMillis()))
                .setReadOverUser(Common.getUserId())
                .setReadOverType(2);
        QueryUserListVO userInfo = sysUserService.getUserInfo(Common.getUserId());
        if(userInfo.getUserId()!= null){
            entity.setReadOverUserName(userInfo.getUserName());
        }
        this.baseMapper.insert(entity);


        //问题列表保存
        if(dto.getQuestionReadOverList()!= null && !dto.getQuestionReadOverList().isEmpty()){
            List<UniversityQuestionReadOver> questionReadOverList = examRecordConvert.toUniversityQuestionReadOverList(dto.getQuestionReadOverList());
            questionReadOverList.forEach(t->t.setReadOverId(entity.getId()));
            universityQuestionReadOverService.batchSave(questionReadOverList);
        }

        //批阅文件批量保存
        List<UniversityReadOverSpotCheckFile> fileList = new ArrayList<>();
        if(dto.getFileList()!= null && !dto.getFileList().isEmpty()){
            for (UniversityReadOverSpotCheckFileSaveDTO fileSaveDTO : dto.getFileList()) {
                UniversityReadOverSpotCheckFile file = new UniversityReadOverSpotCheckFile();
                file.setRecordId(dto.getRecordId())
                        .setObjectType(1)
                        .setObjectId(entity.getId())
                        .setSysVideoMultimediaId(fileSaveDTO.getSysVideoMultimediaId())
                        .setReadOverSort(fileSaveDTO.getReadOverSort());
                fileList.add(file);
            }
            universityReadOverSpotCheckFileService.batchSave(fileList);

            //文件引用
            List<String> addFileIds = fileList.stream().map(t -> t.getSysVideoMultimediaId()).collect(Collectors.toList());
            if(!addFileIds.isEmpty()){
                studyProfileService.useProfile(addFileIds,entity.getId());
            }
        }


        //更新作业记录
        universityHomeworkRecordService.finshHomework(dto);


        UniversityHomeworkRecordVo record = universityHomeworkRecordService.getInfo(dto.getRecordId());
        //是否抽检(0-否;1-是;默认否)
        if(taskHomework.getIsSpotCheck().equals(1)){
            // 如果设置了必须通过，只有通过的作业才需要抽检
            if(taskHomework.getOnlyReadOver() != null && taskHomework.getOnlyReadOver().equals(1)){
                // 必须通过的情况下，只有批阅状态为通过(2)的才抽检
                if(entity.getReadOverStatus().equals(2)){
                    //抽检
                    universityReadOverSpotCheckUserService.saveUserIds(taskHomework.getSpotCheckUserList(), 2,record.getId(), det, Common.getTenantId(), Common.getUserId());
                }
            } else {
                // 非必须通过的情况下，按原逻辑抽检
                //抽检
                universityReadOverSpotCheckUserService.saveUserIds(taskHomework.getSpotCheckUserList(), 2,record.getId(), det, Common.getTenantId(), Common.getUserId());
            }
        }


        // 批阅完成通知
        this.pushReadOverMessage(dto.getRecordId(), task, det, entity.getReadOverStatus());
    }



    // 批阅完成通知
    public void pushReadOverMessage(String recordId, UniversityTaskVO task, UniversityCourseParticipantTaskDet det, Integer readOverStatus){
        String param = null;
        UniversityMessageDTO messageDTO = new UniversityMessageDTO();
        List<UniversityMessageDTO> dtoList = new ArrayList<>();
        Map<String, String> extrasMap = new HashMap<>();
        if (det.getTeachTool().equals(5)){
            // 考试
            param = "testPaperRecordId=" + recordId;
            extrasMap.put("testPaperRecordId", recordId);
            messageDTO.setExamName(task.getTaskName());
        }else {
            // 作业
            param = "homeworkRecordId=" + recordId;
            extrasMap.put("homeworkRecordId", recordId);
            messageDTO.setHomeworkName(task.getTaskName());
        }

        String objectsName = null;
        UniversityCourseParticipantTask userTask = userTaskService.queryById(det.getParticipantTaskId());
        if (Objects.isNull(userTask)) return;

        if (det.getTaskType().equals(0)){
            // 课程
            UniversityCourse course = universityCourseService.lambdaQuery().eq(UniversityCourse::getId, userTask.getActivityId()).one();
            objectsName = Objects.nonNull(course) ? course.getCourseTitle():"";
        }else {
            // 项目
            UniversityProject project = universityProjectService.lambdaQuery().eq(UniversityProject::getId, userTask.getActivityId()).one();
            objectsName = Objects.nonNull(project) ? project.getProjectName():"";
        }

        // 发送通知
        messageDTO.setPushSysUserId(det.getSysUserId())
                .setSysUserId(det.getSysUserId())
                .setObjectsName(objectsName)
                .setObjectsParameter(param).setExtrasMap(extrasMap)
                .setResultType(Objects.nonNull(readOverStatus) && readOverStatus.equals(2) ? 1:0);
        dtoList.add(messageDTO);
        messageRecordService.batchPushMessage(det.getTeachTool().equals(5) ? MessageTypeEnum.MARKED_EXAM:MessageTypeEnum.MARKED_WORK, dtoList);
    }



    /**
     * 查询考试详情
     * @param recordId
     * @return
     */
    @Override
    public UniversityTestPaperRecordAllVo getExam(String recordId) {
        UniversityTestPaperRecordAllVo vo = universityTestPaperRecordService.getById(recordId);

        //批阅信息
        UniversityReadOverSpotCheckVo readOver =  this.getByRecordId(recordId);

        //获取用户任务明细
        UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(vo.getRecord().getUserTaskDetId());
        List<TeachUserVO> userInfoList = sysUserService.findListByUserIds(List.of(det.getSysUserId()));

        //获取部门信息
        List<TeachUserVO> userDepartmentList = sysUserService.findUserDepartmentByUserIds(List.of(det.getSysUserId()));
        TeachUserVO userVO = userInfoList.get(0);
        if (!userDepartmentList.isEmpty()) {
            TeachUserVO departmentVO = userDepartmentList.get(0);
            userVO.setDepartmentName(departmentVO.getDepartmentName());
        }
        vo.setUserVO(userVO);


        if(readOver  == null){
            return vo;
        }
        //批阅题目列表
        List<UniversityQuestionReadOverVo> questionReadOverList = universityQuestionReadOverService.findListByRecordId(recordId);
        //抽检列表
        List<UniversitySpotCheckVo> spotCheckList = universitySpotCheckService.findListByRecordId(recordId);

        //文件列表
        List<UniversityReadOverSpotCheckFileVo> fileVoList = universityReadOverSpotCheckFileService.findListByRecordId(recordId);
        List<String> fileList = fileVoList.stream().map(UniversityReadOverSpotCheckFileVo::getSysVideoMultimediaId).collect(Collectors.toList());
        if(!fileList.isEmpty()){
            //获取附件信息
            List<SysStudyProfileDetailVo> fileDetList = studyProfileService.findDetailByIds(fileList);
            //设置详情到附件关系表里
            Map<String, SysStudyProfileDetailVo> fileDetMap = fileDetList.stream().collect(Collectors.toMap(SysStudyProfileDetailVo::getId, t -> t, (k1, k2) -> k1));
            for (UniversityReadOverSpotCheckFileVo fileVo : fileVoList) {
                SysStudyProfileDetailVo fileDet = fileDetMap.get(fileVo.getSysVideoMultimediaId());
                if(fileDet!= null){
                    fileVo.setFileDetailVo(fileDet);
                }
            }
        }
        Map<String, List<UniversityReadOverSpotCheckFileVo>> fileMap = fileVoList.stream()
                .collect(Collectors.groupingBy(UniversityReadOverSpotCheckFileVo::getObjectId));

        //对象类型(1-批阅)
        List<UniversityReadOverSpotCheckFileVo> readOverFileList = fileVoList.stream().filter(t -> t.getObjectType().equals(1)).collect(Collectors.toList());

//        //设置问题批阅信息列表
//        for (UniversityQuestionReadOverVo readOverVo : questionReadOverList) {
//            List<UniversityReadOverSpotCheckFileVo> fileInfoList = fileMap.get(readOverVo.getId());
//            if(fileInfoList!= null && !fileInfoList.isEmpty()){
//                readOverVo.setFileList(fileInfoList);
//            }
//        }
        //设置批阅信息
        readOver.setQuestionReadOverList(questionReadOverList);
        //设置批阅文件列表
        readOver.setFileList(readOverFileList);
        //存在 批阅信息
        vo .setReadOver(readOver);

        //设置抽检信息列表
        for (UniversitySpotCheckVo spotCheckVo: spotCheckList) {
            List<UniversityReadOverSpotCheckFileVo> fileInfoList = fileMap.get(spotCheckVo.getId());
            if(fileInfoList!= null && !fileInfoList.isEmpty()){
                spotCheckVo.setFileList(fileInfoList);
            }
        }
        //设置抽检信息列表
        vo.setSpotCheckList(spotCheckList);


        Map<String, List<UniversityQuestionReadOverVo>> questionReadOverMap  = questionReadOverList.stream()
                .collect(Collectors.groupingBy(UniversityQuestionReadOverVo::getQuestionId));


        //设置问题处理
        for (SysExamQuestionCollectVo questionCollectVo : vo.getSysExamVo().getQuestionCollect()) {
            for (SysRelExamQuestionVo sysRelExamQuestionVo : questionCollectVo.getRelExamQuestionList()) {
                List<UniversityQuestionReadOverVo> questionReadOverVoList = questionReadOverMap.get(sysRelExamQuestionVo.getId());
                if(questionReadOverVoList!= null && !questionReadOverVoList.isEmpty()){
                    sysRelExamQuestionVo.setQuestionReadOverList(questionReadOverVoList);
                }
            }
        }


        return vo;
    }

    /**
     * 查询考试详情
     * @param recordId
     * @return
     */
    @Override
    public UniversityHomeworkRecordAllVo getHomework(String recordId) {
        UniversityHomeworkRecordAllVo vo = universityHomeworkRecordService.getById(recordId);

        //批阅信息
        UniversityReadOverSpotCheckVo readOver =  this.getByRecordId(recordId);

        //获取用户任务明细
        UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(vo.getRecord().getUserTaskDetId());
        List<TeachUserVO> userInfoList = sysUserService.findListByUserIds(List.of(det.getSysUserId()));

        //获取部门信息
        List<TeachUserVO> userDepartmentList = sysUserService.findUserDepartmentByUserIds(List.of(det.getSysUserId()));
        TeachUserVO userVO = userInfoList.get(0);
        if (!userDepartmentList.isEmpty()) {
            TeachUserVO departmentVO = userDepartmentList.get(0);
            userVO.setDepartmentName(departmentVO.getDepartmentName());
        }
        vo.setUserVO(userVO);

        //批阅题目列表
        List<UniversityQuestionReadOverVo> questionReadOverList = universityQuestionReadOverService.findListByRecordId(recordId);
        //抽检列表
        List<UniversitySpotCheckVo> spotCheckList = universitySpotCheckService.findListByRecordId(recordId);

        //文件列表
        List<UniversityReadOverSpotCheckFileVo> fileVoList = universityReadOverSpotCheckFileService.findListByRecordId(recordId);
        List<String> fileList = fileVoList.stream().map(UniversityReadOverSpotCheckFileVo::getSysVideoMultimediaId).collect(Collectors.toList());
        if(!fileList.isEmpty()){
            //获取附件信息
            List<SysStudyProfileDetailVo> fileDetList = studyProfileService.findDetailByIds(fileList);
            //设置详情到附件关系表里
            Map<String, SysStudyProfileDetailVo> fileDetMap = fileDetList.stream().collect(Collectors.toMap(SysStudyProfileDetailVo::getId, t -> t, (k1, k2) -> k1));
            for (UniversityReadOverSpotCheckFileVo fileVo : fileVoList) {
                SysStudyProfileDetailVo fileDet = fileDetMap.get(fileVo.getSysVideoMultimediaId());
                if(fileDet!= null){
                    fileVo.setFileDetailVo(fileDet);
                }
            }
        }
        Map<String, List<UniversityReadOverSpotCheckFileVo>> fileMap = fileVoList.stream()
                .collect(Collectors.groupingBy(UniversityReadOverSpotCheckFileVo::getObjectId));

        //对象类型(1-批阅)
        List<UniversityReadOverSpotCheckFileVo> readOverFileList = fileVoList.stream().filter(t -> t.getObjectType().equals(1)).collect(Collectors.toList());

//        //设置问题批阅信息列表
//        for (UniversityQuestionReadOverVo readOverVo : questionReadOverList) {
//            List<UniversityReadOverSpotCheckFileVo> fileInfoList = fileMap.get(readOverVo.getId());
//            if(fileInfoList!= null && !fileInfoList.isEmpty()){
//                readOverVo.setFileList(fileInfoList);
//            }
//        }
        if(readOver != null){
            //设置批阅信息
            readOver.setQuestionReadOverList(questionReadOverList);
            //设置批阅文件列表
            readOver.setFileList(readOverFileList);
            //存在 批阅信息
            vo .setReadOver(readOver);
        }

        //设置抽检信息列表
        for (UniversitySpotCheckVo spotCheckVo: spotCheckList) {
            List<UniversityReadOverSpotCheckFileVo> fileInfoList = fileMap.get(spotCheckVo.getId());
            if(fileInfoList!= null && !fileInfoList.isEmpty()){
                spotCheckVo.setFileList(fileInfoList);
            }
        }
        //设置抽检信息列表
        vo.setSpotCheckList(spotCheckList);


        Map<String, List<UniversityQuestionReadOverVo>> questionReadOverMap  = questionReadOverList.stream()
                .collect(Collectors.groupingBy(UniversityQuestionReadOverVo::getQuestionId));

        //设置问题处理
        for (UniversityHomeworkSubmitQuestionVo homeworkSubmitQuestion : vo.getQuestionList()) {
            List<UniversityQuestionReadOverVo> questionReadOverVoList = questionReadOverMap.get(homeworkSubmitQuestion.getQuestionId());
            if(questionReadOverVoList!= null && !questionReadOverVoList.isEmpty()){
                homeworkSubmitQuestion.setQuestionReadOverList(questionReadOverVoList);
            }
        }
        return vo;
    }



    /**
     * 通记录Id查看vo
     * @param recordId
     * @return
     */
    @Override
    public UniversityReadOverSpotCheckVo getByRecordId(String recordId) {
        return this.baseMapper.getByRecordId(recordId);
    }






    /**
     * 考试批阅查询
     * @param dto
     * @return
     */
    @Override
    public UniversityExamReadOverAllVo examFindList(UniversityExamReadOverFindDTO dto) {
        UniversityExamReadOverAllVo vo = new UniversityExamReadOverAllVo();
        dto.setUserId(Common.getUserId());
        //门店转部门
        if (dto.getShopIdList()!=null&&dto.getShopIdList().size()>0) {
            QueryShopBaseInfoListDTO queryShopBaseInfoListDTO = new QueryShopBaseInfoListDTO();
            queryShopBaseInfoListDTO.setShopIdList(dto.getShopIdList());
            List<QueryShopBaseInfoListVO> baseInfoListVOS = FeignUtils.processFeignResult(shopBaseClient.findList(queryShopBaseInfoListDTO));
            if (baseInfoListVOS==null||baseInfoListVOS.isEmpty()) {
                return null;
            }
            dto.setDeptIdList(baseInfoListVOS.stream().map(QueryShopBaseInfoListVO::getSysDeptId).collect(Collectors.toList()));
        }
        //未审批列表
        List<UniversityReadOverVo> unList = this.baseMapper.findUnReadOverExamList(dto);
        //全部审批列表
        List<UniversityReadOverVo> allReadOverList = this.baseMapper.findAllReadOverExamList(dto);


        //学员id列表
        List<String> userIdList = unList.stream().map(UniversityReadOverVo::getUserId).collect(Collectors.toList());
        userIdList.addAll(allReadOverList.stream().map(UniversityReadOverVo::getUserId).collect(Collectors.toList()));
        userIdList = userIdList.stream().distinct().collect(Collectors.toList());

        if(!userIdList.isEmpty()){
            //用户信息列表
            List<TeachUserVO> userInfoList = sysUserService.findListByUserIds(userIdList);
            Map<String,TeachUserVO> userMap = userInfoList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            //部门信息列表
            List<TeachUserVO> userDepartmentList = sysUserService.findUserDepartmentByUserIds(userIdList);
            Map<String,TeachUserVO> departmentMap = userDepartmentList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            //设置用户信息
            for (UniversityReadOverVo readOverVo : unList) {
                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
                if(userVO!= null){
                    readOverVo.setUserName(userVO.getUserName());
                    readOverVo.setShopId(userVO.getShopId());
                    readOverVo.setShopName(userVO.getShopName());
                }
                TeachUserVO departmentVO = departmentMap.get(readOverVo.getUserId());
                if(departmentVO!= null){
                    readOverVo.setDepartmentName(departmentVO.getDepartmentName());
                }
            }
            //设置用户信息
            for (UniversityReadOverVo readOverVo : allReadOverList) {
                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
                if(userVO!= null){
                    readOverVo.setUserName(userVO.getUserName());
                    readOverVo.setShopId(userVO.getShopId());
                    readOverVo.setShopName(userVO.getShopName());
                }
                TeachUserVO departmentVO = departmentMap.get(readOverVo.getUserId());
                if(departmentVO!= null){
                    readOverVo.setDepartmentName(departmentVO.getDepartmentName());
                }
            }
        }

        //未审批列表
        unList = unList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());
        //全部审批列表
        allReadOverList = allReadOverList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());
        //已通过列表
        List<UniversityReadOverVo> passList = allReadOverList.stream().filter(t->t.getRecordStatus().equals(2)).collect(Collectors.toList());
        //未通过列表
        List<UniversityReadOverVo> unPassList = allReadOverList.stream().filter(t->t.getRecordStatus().equals(3)).collect(Collectors.toList());
        //全部列表
        List<UniversityReadOverVo> allList = new ArrayList<>();
        allList.addAll(unList);
        allList.addAll(allReadOverList);
        allList = allList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());

        //设置返回列表
        vo.setUnList(unList);
        vo.setPassList(passList);
        vo.setUnPassList(unPassList);
        vo.setAllList(allList);
        return vo;
    }



    /**
     * 作业批阅查询
     * @param dto
     * @return
     */
    @Override
    public UniversityExamReadOverAllVo homeworkFindList(UniversityHomeworkReadOverFindDTO dto) {
        UniversityExamReadOverAllVo vo = new UniversityExamReadOverAllVo();
        dto.setUserId(Common.getUserId());
        //门店转部门
        if (dto.getShopIdList()!=null&&dto.getShopIdList().size()>0) {
            QueryShopBaseInfoListDTO queryShopBaseInfoListDTO = new QueryShopBaseInfoListDTO();
            queryShopBaseInfoListDTO.setShopIdList(dto.getShopIdList());
            List<QueryShopBaseInfoListVO> baseInfoListVOS = FeignUtils.processFeignResult(shopBaseClient.findList(queryShopBaseInfoListDTO));
            if (baseInfoListVOS==null||baseInfoListVOS.isEmpty()) {
                return null;
            }
            dto.setDeptIdList(baseInfoListVOS.stream().map(QueryShopBaseInfoListVO::getSysDeptId).collect(Collectors.toList()));
        }
        //未审批列表
        List<UniversityReadOverVo> unList = this.baseMapper.findUnReadOverHomeworkList(dto);
        //全部审批列表
        List<UniversityReadOverVo> allReadOverList = this.baseMapper.findAllReadOverHomeworkList(dto);


        //学员id列表
        List<String> userIdList = unList.stream().map(UniversityReadOverVo::getUserId).collect(Collectors.toList());
        userIdList.addAll(allReadOverList.stream().map(UniversityReadOverVo::getUserId).collect(Collectors.toList()));
        userIdList = userIdList.stream().distinct().collect(Collectors.toList());

        if(!userIdList.isEmpty()){
            //用户信息列表
            List<TeachUserVO> userInfoList = sysUserService.findListByUserIds(userIdList);
            Map<String,TeachUserVO> userMap = userInfoList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            //部门信息列表
            List<TeachUserVO> userDepartmentList = sysUserService.findUserDepartmentByUserIds(userIdList);
            Map<String,TeachUserVO> departmentMap = userDepartmentList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            //设置用户信息
            for (UniversityReadOverVo readOverVo : unList) {
                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
                if(userVO!= null){
                    readOverVo.setUserName(userVO.getUserName());
                    readOverVo.setShopId(userVO.getShopId());
                    readOverVo.setShopName(userVO.getShopName());
                }
                TeachUserVO departmentVO = departmentMap.get(readOverVo.getUserId());
                if(departmentVO!= null){
                    readOverVo.setDepartmentName(departmentVO.getDepartmentName());
                }
            }
            //设置用户信息
            for (UniversityReadOverVo readOverVo : allReadOverList) {
                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
                if(userVO!= null){
                    readOverVo.setUserName(userVO.getUserName());
                    readOverVo.setShopId(userVO.getShopId());
                    readOverVo.setShopName(userVO.getShopName());
                }
                TeachUserVO departmentVO = departmentMap.get(readOverVo.getUserId());
                if(departmentVO!= null){
                    readOverVo.setDepartmentName(departmentVO.getDepartmentName());
                }
            }
        }

        //未审批列表
        unList = unList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());
        //全部审批列表
        allReadOverList = allReadOverList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());
        //已通过列表
        List<UniversityReadOverVo> passList = allReadOverList.stream().filter(t->t.getRecordStatus().equals(2)).collect(Collectors.toList());
        //未通过列表
        List<UniversityReadOverVo> unPassList = allReadOverList.stream().filter(t->t.getRecordStatus().equals(3)).collect(Collectors.toList());
        //全部列表
        List<UniversityReadOverVo> allList = new ArrayList<>();
        allList.addAll(unList);
        allList.addAll(allReadOverList);
        allList = allList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());

        //设置返回列表
        vo.setUnList(unList);
        vo.setPassList(passList);
        vo.setUnPassList(unPassList);
        vo.setAllList(allList);
        return vo;
    }



    /**
     * 考试批阅集合查询
     * @param dto
     * @return
     */
    @Override
    public UniversityExamReadOverCollectVo examCollect(UniversityExamReadOverFindDTO dto) {
        UniversityExamReadOverCollectVo vo = new UniversityExamReadOverCollectVo();
        dto.setUserId(Common.getUserId());
        //dto.setUserId("07887abecc36ff36af9d18f8e485e072");

        //门店转部门
        if (dto.getShopIdList()!=null&&dto.getShopIdList().size()>0) {
            QueryShopBaseInfoListDTO queryShopBaseInfoListDTO = new QueryShopBaseInfoListDTO();
            queryShopBaseInfoListDTO.setShopIdList(dto.getShopIdList());
            List<QueryShopBaseInfoListVO> baseInfoListVOS = FeignUtils.processFeignResult(shopBaseClient.findList(queryShopBaseInfoListDTO));
            if (baseInfoListVOS==null||baseInfoListVOS.isEmpty()) {
                return null;
            }
            dto.setDeptIdList(baseInfoListVOS.stream().map(QueryShopBaseInfoListVO::getSysDeptId).collect(Collectors.toList()));
        }

        //未审批列表
        List<UniversityReadOverVo> unReadOverList = this.baseMapper.findUnReadOverExamList(dto);
        //全部审批列表
        List<UniversityReadOverVo> allReadOverList = this.baseMapper.findAllReadOverExamList(dto);


        //学员id列表
        List<String> userIdList = unReadOverList.stream().map(UniversityReadOverVo::getUserId).collect(Collectors.toList());
        userIdList.addAll(allReadOverList.stream().map(UniversityReadOverVo::getUserId).collect(Collectors.toList()));
        userIdList = userIdList.stream().distinct().collect(Collectors.toList());


//        if(!userIdList.isEmpty()){
//            //用户信息列表
//            List<TeachUserVO> userInfoList = sysUserService.findListByUserIds(userIdList);
//            Map<String,TeachUserVO> userMap = userInfoList.stream()
//                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));
//
//            //设置用户信息
//            for (UniversityReadOverVo readOverVo : unReadOverList) {
//                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
//                if(userVO!= null){
//                    readOverVo.setUserName(userVO.getUserName());
//                    readOverVo.setShopId(userVO.getShopId());
//                    readOverVo.setShopName(userVO.getShopName());
//                }
//            }
//            //设置用户信息
//            for (UniversityReadOverVo readOverVo : allReadOverList) {
//                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
//                if(userVO!= null){
//                    readOverVo.setUserName(userVO.getUserName());
//                    readOverVo.setShopId(userVO.getShopId());
//                    readOverVo.setShopName(userVO.getShopName());
//                }
//            }
//        }

        //未审批列表
        unReadOverList = unReadOverList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());
        //全部审批列表
        allReadOverList = allReadOverList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());
//        //已通过列表
//        List<UniversityReadOverVo> passList = allReadOverList.stream().filter(t->t.getRecordStatus().equals(2)).collect(Collectors.toList());
//        //未通过列表
//        List<UniversityReadOverVo> unPassList = allReadOverList.stream().filter(t->t.getRecordStatus().equals(3)).collect(Collectors.toList());
        //全部列表
        List<UniversityReadOverVo> allList = new ArrayList<>();
        allList.addAll(unReadOverList);
        allList.addAll(allReadOverList);
        allList = allList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());



        //未审批列表
        Map<String,List<UniversityReadOverVo>> unMap = unReadOverList.stream().collect(Collectors.groupingBy(UniversityReadOverVo::getTaskId));

        //已审批列表
        Map<String,List<UniversityReadOverVo>> overMap = allReadOverList.stream().collect(Collectors.groupingBy(UniversityReadOverVo::getTaskId));

        //全部列表
        Map<String,List<UniversityReadOverVo>> allMap = allList.stream().collect(Collectors.groupingBy(UniversityReadOverVo::getTaskId));

        vo.setUnMap(unMap);
        vo.setOverMap(overMap);
        vo.setAllMap(allMap);
        return vo;
    }

    /**
     * 作业批阅集合查询
     *
     * @param dto
     * @return
     */
    @Override
    public UniversityHomeworkReadOverAllCollectVo homeworkCollect(UniversityHomeworkReadOverFindDTO dto) {
        UniversityHomeworkReadOverAllCollectVo vo = new UniversityHomeworkReadOverAllCollectVo();
        dto.setUserId(Common.getUserId());
        //门店转部门
        if (dto.getShopIdList()!=null&&dto.getShopIdList().size()>0) {
            QueryShopBaseInfoListDTO queryShopBaseInfoListDTO = new QueryShopBaseInfoListDTO();
            queryShopBaseInfoListDTO.setShopIdList(dto.getShopIdList());
            List<QueryShopBaseInfoListVO> baseInfoListVOS = FeignUtils.processFeignResult(shopBaseClient.findList(queryShopBaseInfoListDTO));
            if (baseInfoListVOS==null||baseInfoListVOS.isEmpty()) {
                return null;
            }
            dto.setDeptIdList(baseInfoListVOS.stream().map(QueryShopBaseInfoListVO::getSysDeptId).collect(Collectors.toList()));
        }


        //未审批列表
        List<UniversityReadOverVo> unReadOverList = this.baseMapper.findUnReadOverHomeworkList(dto);
        //全部审批列表
        List<UniversityReadOverVo> allReadOverList = this.baseMapper.findAllReadOverHomeworkList(dto);


        //学员id列表
        List<String> userIdList = unReadOverList.stream().map(UniversityReadOverVo::getUserId).collect(Collectors.toList());
        userIdList.addAll(allReadOverList.stream().map(UniversityReadOverVo::getUserId).collect(Collectors.toList()));
        userIdList = userIdList.stream().distinct().collect(Collectors.toList());


        if(!userIdList.isEmpty()){
            //用户信息列表
            List<TeachUserVO> userInfoList = sysUserService.findListByUserIds(userIdList);
            Map<String,TeachUserVO> userMap = userInfoList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            //部门信息列表
            List<TeachUserVO> userDepartmentList = sysUserService.findUserDepartmentByUserIds(userIdList);
            Map<String,TeachUserVO> departmentMap = userDepartmentList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            //设置用户信息
            for (UniversityReadOverVo readOverVo : unReadOverList) {
                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
                if(userVO!= null){
                    readOverVo.setUserName(userVO.getUserName());
                    readOverVo.setShopId(userVO.getShopId());
                    readOverVo.setShopName(userVO.getShopName());
                }
                TeachUserVO departmentVO = departmentMap.get(readOverVo.getUserId());
                if(departmentVO!= null){
                    readOverVo.setDepartmentName(departmentVO.getDepartmentName());
                }
            }
            //设置用户信息
            for (UniversityReadOverVo readOverVo : allReadOverList) {
                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
                if(userVO!= null){
                    readOverVo.setUserName(userVO.getUserName());
                    readOverVo.setShopId(userVO.getShopId());
                    readOverVo.setShopName(userVO.getShopName());
                }
                TeachUserVO departmentVO = departmentMap.get(readOverVo.getUserId());
                if(departmentVO!= null){
                    readOverVo.setDepartmentName(departmentVO.getDepartmentName());
                }
            }
        }

        //未审批列表
        unReadOverList = unReadOverList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());
        //全部审批列表
        allReadOverList = allReadOverList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());
//        //已通过列表
//        List<UniversityReadOverVo> passList = allReadOverList.stream().filter(t->t.getRecordStatus().equals(2)).collect(Collectors.toList());
//        //未通过列表
//        List<UniversityReadOverVo> unPassList = allReadOverList.stream().filter(t->t.getRecordStatus().equals(3)).collect(Collectors.toList());
        //全部列表
        List<UniversityReadOverVo> allList = new ArrayList<>();
        allList.addAll(unReadOverList);
        allList.addAll(allReadOverList);
        allList = allList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed()).collect(Collectors.toList());



        //未审批列表
        Map<String,List<UniversityReadOverVo>> unMap = unReadOverList.stream().collect(Collectors.groupingBy(UniversityReadOverVo::getTaskId));

        //已审批列表
        Map<String,List<UniversityReadOverVo>> overMap = allReadOverList.stream().collect(Collectors.groupingBy(UniversityReadOverVo::getTaskId));

        //全部列表
        Map<String,List<UniversityReadOverVo>> allMap = allList.stream().collect(Collectors.groupingBy(UniversityReadOverVo::getTaskId));



        //未审批列表
        List<UniversityHomeworkReadOverCollectVo> unCollectList = new ArrayList<>();
        //已审批列表
        List<UniversityHomeworkReadOverCollectVo> overCollectList = new ArrayList<>();
        //全部列表
        List<UniversityHomeworkReadOverCollectVo> allCollectList = new ArrayList<>();


        //未审批列表
        for (Map.Entry<String, List<UniversityReadOverVo>> en : unMap.entrySet()) {
            List<UniversityReadOverVo> list = en.getValue();
            UniversityReadOverVo universityReadOverVo = list.get(0);
            UniversityHomeworkReadOverCollectVo collectVo = new UniversityHomeworkReadOverCollectVo();
            collectVo.setTaskId(universityReadOverVo.getTaskId());
            collectVo.setTaskName(universityReadOverVo.getTaskName());
            collectVo.setRecordNum(list.size());
            collectVo.setIsReadOver(0);
            unCollectList.add(collectVo);
        }
        //已审批列表
        for (Map.Entry<String, List<UniversityReadOverVo>> en : overMap.entrySet()) {
            List<UniversityReadOverVo> list = en.getValue();
            UniversityReadOverVo universityReadOverVo = list.get(0);
            UniversityHomeworkReadOverCollectVo collectVo = new UniversityHomeworkReadOverCollectVo();
            collectVo.setTaskId(universityReadOverVo.getTaskId());
            collectVo.setTaskName(universityReadOverVo.getTaskName());
            collectVo.setRecordNum(list.size());
            collectVo.setIsReadOver(1);
            overCollectList.add(collectVo);
        }
        //全部列表
        for (Map.Entry<String, List<UniversityReadOverVo>> en : allMap.entrySet()) {
            List<UniversityReadOverVo> list = en.getValue();
            UniversityReadOverVo universityReadOverVo = list.get(0);
            UniversityHomeworkReadOverCollectVo collectVo = new UniversityHomeworkReadOverCollectVo();

            List<UniversityReadOverVo> universityReadOverVos = unMap.get(universityReadOverVo.getTaskId());
            if(universityReadOverVos != null && !universityReadOverVos.isEmpty()){
                collectVo.setIsReadOver(0);
            }else{
                collectVo.setIsReadOver(1);
            }
            collectVo.setTaskId(universityReadOverVo.getTaskId());
            collectVo.setTaskName(universityReadOverVo.getTaskName());
            collectVo.setRecordNum(list.size());
            allCollectList.add(collectVo);
        }


        //设置列表
        vo.setUnCollectList(unCollectList);
        vo.setOverCollectList(overCollectList);
        vo.setAllCollectList(allCollectList);
        return vo;
    }



    /**
     * 机批保存
     *
     * @param dto
     * @param qualifyScore
     * @param task
     * @param det
     * @param now
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pcReadOverSave(UniversityPcReadOverSaveDTO dto, BigDecimal qualifyScore, UniversityTaskVO task, UniversityCourseParticipantTaskDet det, Timestamp now) {
        log.info("机批dto={},qualifyScore={},det={},task={},now={}",dto,qualifyScore,det,task,now);

        //是否被批阅
        QueryWrapper<UniversityReadOverSpotCheck> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UniversityReadOverSpotCheck::getRecordId,dto.getRecordId());
        List<UniversityReadOverSpotCheck> list = getBaseMapper().selectList(queryWrapper);
        if(!list.isEmpty()){
            if(dto.getReadOverType().equals(1)){
                SysHttpResult.ERROR_UNIVERSITY_EXAM_IS_READ_OVER.appBizException();
            }else if(dto.getReadOverType().equals(2)){
                SysHttpResult.ERROR_UNIVERSITY_HOMEWORK_IS_READ_OVER.appBizException();
            }
        }

        //保存批阅主表
        UniversityReadOverSpotCheck entity = new UniversityReadOverSpotCheck();
        BeanUtils.copyProperties(dto,entity);
        entity.setReadOverTime(new Timestamp(System.currentTimeMillis()))
                .setOperateType(1)
                .setReadOverUser("1")
                .setReadOverUserName("系统");
        this.baseMapper.insert(entity);



        //完成条件(4.分数达标)   && 达标分数  && 达标分数>=及格分
        if(task.getTeachTool().equals(5) && task.getCompleteCondition().equals(4)
                &&  qualifyScore!= null && dto.getReadOverScore()!= null &&  dto.getReadOverScore().compareTo(qualifyScore)>=0){

            //调用完成功能
            UniversityUserTaskDetUpdateVO detUpdateVO = new UniversityUserTaskDetUpdateVO();
            BeanUtils.copyProperties(det,detUpdateVO);
            detUpdateVO.setProgress(BigDecimal.valueOf(100));
            if (det.getTaskType().equals(1) && det.getIsExpireStudy().equals(1) && now.after(det.getEndTime())) {
                detUpdateVO.setTaskStatus(4);
            }else{
                detUpdateVO.setTaskStatus(2);
            }
            log.info("机批完成调用={}",detUpdateVO);
            universityUserTaskDetService.taskUpdateProgressAll(detUpdateVO);
        }
        log.info("机批完成={}",entity);


        // 批阅完成通知
        this.pushReadOverMessage(dto.getRecordId(), task, det, entity.getReadOverStatus());
    }




    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> getHomeworkSpotCheck(String recordId, Integer isSave) {
        List<String> userIds = new ArrayList<>();
        //作业任务
        UniversityHomeworkRecordVo universityHomeworkRecordVo = universityHomeworkRecordService.get(recordId);
        //获取用户任务
        UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(universityHomeworkRecordVo.getUserTaskDetId());
        //作业任务
        UniversityTaskVO task = universityTaskService.queryById(det.getDetailId());
        UniversityTaskHomeworkVo taskHomework = task.getTaskHomework();

        //是否抽检(0-否;1-是;默认否)
        if(taskHomework.getIsSpotCheck().equals(1)){
            //抽检
            userIds = universityReadOverSpotCheckUserService.getUserIds(taskHomework.getSpotCheckUserList(),2,universityHomeworkRecordVo.getId(), det, isSave);
        }
        return userIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> getExamSpotCheck(String recordId, Integer isSave) {
        List<String> userIds = new ArrayList<>();
        UniversityTestPaperRecordVo record = universityTestPaperRecordService.getInfo(recordId);
        //获取用户任务明细
        UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(record.getUserTaskDetId());
        //考试任务
        UniversityTaskVO task = universityTaskService.queryById(det.getDetailId());
        UniversityTaskExamVo taskExamVo = task.getTaskExam();
        //是否抽检(0-否;1-是;默认否)
        if(taskExamVo.getIsSpotCheck().equals(1)){
            //抽检
            userIds = universityReadOverSpotCheckUserService.getUserIds(taskExamVo.getSpotCheckUserList(),2,record.getId(), det, isSave);
        }
        return userIds;
    }
}



