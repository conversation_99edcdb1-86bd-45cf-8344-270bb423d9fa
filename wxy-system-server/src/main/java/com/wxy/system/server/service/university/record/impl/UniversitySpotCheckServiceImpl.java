package com.wxy.system.server.service.university.record.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wxy.base.exception.AppBizException;
import com.wxy.base.util.Common;
import com.wxy.base.util.FeignUtils;
import com.wxy.base.util.StringUtils;
import com.wxy.shop.common.dto.base.QueryShopBaseInfoListDTO;
import com.wxy.shop.common.vo.base.QueryShopBaseInfoListVO;
import com.wxy.shop.feign.client.base.ShopBaseClient;
import com.wxy.system.common.dto.university.record.UniversityReadOverSpotCheckFileSaveDTO;
import com.wxy.system.common.dto.university.record.spotcheck.UniversitySpotCheckFindDTO;
import com.wxy.system.common.dto.university.record.spotcheck.UniversitySpotCheckSaveDTO;
import com.wxy.system.common.vo.exam.SysStudyProfileDetailVo;
import com.wxy.system.common.vo.university.record.TeachUserVO;
import com.wxy.system.common.vo.university.record.UniversityReadOverVo;
import com.wxy.system.common.vo.university.record.UniversitySpotCheckVo;
import com.wxy.system.common.vo.university.record.spotcheck.UniversitySpotCheckCollectVo;
import com.wxy.system.common.vo.university.record.spotcheck.UniversitySpotCheckListVo;
import com.wxy.system.common.vo.university.record.spotcheck.UniversitySpotCheckRateVo;
import com.wxy.system.common.vo.university.task.UniversityHomeworkUserSubmitDetSpotVo;
import com.wxy.system.common.vo.university.task.UniversityTaskVO;
import com.wxy.system.common.vo.user.QueryUserListVO;
import com.wxy.system.server.dao.university.project.UniversitySpotCheckDao;
import com.wxy.system.server.po.university.record.UniversityReadOverSpotCheckFile;
import com.wxy.system.server.po.university.record.UniversitySpotCheck;
import com.wxy.system.server.service.study.profile.ISysStudyProfileService;
import com.wxy.system.server.service.university.record.IUniversityReadOverSpotCheckFileService;
import com.wxy.system.server.service.university.record.IUniversitySpotCheckService;
import com.wxy.system.server.service.university.task.IUniversityTaskService;
import com.wxy.system.server.service.user.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 企业大学-抽检表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-08 10:51:05
 */

@Slf4j
@Service
public class UniversitySpotCheckServiceImpl extends ServiceImpl<UniversitySpotCheckDao, UniversitySpotCheck> implements IUniversitySpotCheckService   {


    @Resource
    private ISysStudyProfileService studyProfileService;

    @Resource
    private IUniversityReadOverSpotCheckFileService universityReadOverSpotCheckFileService;

    @Resource
    private IUniversityTaskService universityTaskService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private ShopBaseClient shopBaseClient;
    /**
     * 考试抽检保存
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String examSpotCheckSave(UniversitySpotCheckSaveDTO dto) throws AppBizException {
        //是否被抽检
        QueryWrapper<UniversitySpotCheck> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UniversitySpotCheck::getRecordId,dto.getRecordId())
                .eq(UniversitySpotCheck::getSpotCheckUser,Common.getUserId());
        List<UniversitySpotCheck> list = getBaseMapper().selectList(queryWrapper);
        if(!list.isEmpty()){
            throw new AppBizException("999000","该记录已被抽检,无法再次抽检", StringUtils.EMPTY);
            //SysHttpResult.ERROR_UNIVERSITY_HOMEWORK_IS_READ_OVER.appBizException();
        }

        //保存批阅主表
        UniversitySpotCheck entity = new UniversitySpotCheck();
        BeanUtils.copyProperties(dto,entity);
        entity.setSpotCheckTime(new Timestamp(System.currentTimeMillis()))
                .setSpotCheckUser(Common.getUserId())
                .setSpotCheckType(1);
        QueryUserListVO userInfo = sysUserService.getUserInfo(Common.getUserId());
        if(userInfo.getUserId()!= null){
            entity.setSpotCheckUserName(userInfo.getUserName());
        }
        this.baseMapper.insert(entity);



        //批阅文件批量保存
        List<UniversityReadOverSpotCheckFile> fileList = new ArrayList<>();
        if(dto.getFileList()!= null && !dto.getFileList().isEmpty()){
            for (UniversityReadOverSpotCheckFileSaveDTO fileSaveDTO : dto.getFileList()) {
                UniversityReadOverSpotCheckFile file = new UniversityReadOverSpotCheckFile();
                file.setObjectType(2)
                        .setRecordId(dto.getRecordId())
                        .setObjectId(entity.getId())
                        .setSysVideoMultimediaId(fileSaveDTO.getSysVideoMultimediaId())
                        .setReadOverSort(fileSaveDTO.getReadOverSort());
                fileList.add(file);
            }
            universityReadOverSpotCheckFileService.saveBatch(fileList);

            //批量引用
            List<String> addFileIds = fileList.stream().map(t -> t.getSysVideoMultimediaId()).collect(Collectors.toList());
            if(!addFileIds.isEmpty()){
                studyProfileService.useProfile(addFileIds,entity.getId());
            }
        }

        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String homeworSpotCheckSave(UniversitySpotCheckSaveDTO dto) {
        //是否被抽检
        QueryWrapper<UniversitySpotCheck> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UniversitySpotCheck::getRecordId,dto.getRecordId())
                .eq(UniversitySpotCheck::getSpotCheckUser,Common.getUserId());
        List<UniversitySpotCheck> list = getBaseMapper().selectList(queryWrapper);
        if(!list.isEmpty()){
            throw new AppBizException("999000","该记录已被抽检,无法再次抽检", StringUtils.EMPTY);
            //SysHttpResult.ERROR_UNIVERSITY_HOMEWORK_IS_READ_OVER.appBizException();
        }

        //保存批阅主表
        UniversitySpotCheck entity = new UniversitySpotCheck();
        BeanUtils.copyProperties(dto,entity);
        entity.setSpotCheckTime(new Timestamp(System.currentTimeMillis()))
                .setSpotCheckUser(Common.getUserId())
                .setSpotCheckType(2);
        QueryUserListVO userInfo = sysUserService.getUserInfo(Common.getUserId());
        if(userInfo.getUserId()!= null){
            entity.setSpotCheckUserName(userInfo.getUserName());
        }
        this.baseMapper.insert(entity);



        //批阅文件批量保存
        List<UniversityReadOverSpotCheckFile> fileList = new ArrayList<>();
        if(dto.getFileList()!= null && !dto.getFileList().isEmpty()){
            for (UniversityReadOverSpotCheckFileSaveDTO fileSaveDTO : dto.getFileList()) {
                UniversityReadOverSpotCheckFile file = new UniversityReadOverSpotCheckFile();
                file.setObjectType(2)
                        .setRecordId(dto.getRecordId())
                        .setObjectId(entity.getId())
                        .setSysVideoMultimediaId(fileSaveDTO.getSysVideoMultimediaId())
                        .setReadOverSort(fileSaveDTO.getReadOverSort());
                fileList.add(file);
            }

            universityReadOverSpotCheckFileService.saveBatch(fileList);

            //批量引用
            List<String> addFileIds = fileList.stream().map(t -> t.getSysVideoMultimediaId()).collect(Collectors.toList());
            if(!addFileIds.isEmpty()){
                studyProfileService.useProfile(addFileIds,entity.getId());
            }
        }

        return entity.getId();
    }


    /**
     * 查询
     * @param id
     * @return
     */
    @Override
    public UniversitySpotCheck getById(String id){
        return getBaseMapper().getById(id);
    }

    /**
     * 分页查询
     * @param page
     * @param dto
     * @return
     */
    @Override
    public IPage<UniversitySpotCheck> findPage(IPage page, UniversitySpotCheckFindDTO dto){
        return getBaseMapper().findPage(page,dto);
    }

    /**
     * 删除
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(UniversitySpotCheck entity){
        this.getBaseMapper().deleteByIdWithFill(entity);
    }

     /**
     * 批量保存
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSave(List<UniversitySpotCheck> list){
        return this.saveBatch(list);
    }


    /**
     * 批量更新
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdate(List<UniversitySpotCheck> list){
        list.forEach(t->
                t.setUpdateTime(null)
                .setUpdateUser(null)
        );
        return this.updateBatchById(list);
    }

    @Override
    public List<UniversityHomeworkUserSubmitDetSpotVo> findByRecordId(String id) {

        List<UniversityHomeworkUserSubmitDetSpotVo> spotVoList= this.getBaseMapper().findByRecordId(id);


        if (!spotVoList.isEmpty()) {
                List<SysStudyProfileDetailVo> detail = studyProfileService.findSpotDetailByIds(List.of(id));
                Map<String, List<SysStudyProfileDetailVo>> detProfileVoMap = detail.stream().collect(Collectors.groupingBy(SysStudyProfileDetailVo::getObjectId));
                for (UniversityHomeworkUserSubmitDetSpotVo universityHomeworkUserSubmitDetSpotVo : spotVoList) {
                    universityHomeworkUserSubmitDetSpotVo.setStudyProfileDetailVos(detProfileVoMap.get(universityHomeworkUserSubmitDetSpotVo.getId()));
                }
        }

        return spotVoList;
    }




    /**
     * 抽检列表
     * @param recordId
     * @return
     */
    @Override
    public List<UniversitySpotCheckVo> findListByRecordId(String recordId) {
        return this.baseMapper.findListByRecordId(recordId);
    }




    /**
     * 抽检集合查询
     *
     * @param page 分页参数
     * @param dto 查询条件
     * @return
     */
    @Override
    public IPage<UniversitySpotCheckCollectVo> findCollect(IPage<UniversitySpotCheckCollectVo> page, UniversitySpotCheckFindDTO dto) {
        dto.setUserId(Common.getUserId());
        //门店转部门
        if (dto.getShopIdList()!=null&&dto.getShopIdList().size()>0) {
            QueryShopBaseInfoListDTO queryShopBaseInfoListDTO = new QueryShopBaseInfoListDTO();
            queryShopBaseInfoListDTO.setShopIdList(dto.getShopIdList());
            List<QueryShopBaseInfoListVO> baseInfoListVOS = FeignUtils.processFeignResult(shopBaseClient.findList(queryShopBaseInfoListDTO));
            if (baseInfoListVOS==null||baseInfoListVOS.isEmpty()) {
                return new Page<>();
            }
            dto.setDeptIdList(baseInfoListVOS.stream().map(QueryShopBaseInfoListVO::getSysDeptId).collect(Collectors.toList()));
        }

        List<UniversityReadOverVo> allList;
        long totalCount = 0;

        // 判断是否需要去重查询
        if (dto.getProjectScheduling() == null) {
            // 进行去重查询，不需要分组
            if (dto.getQueryType() != null && dto.getQueryType().equals(1)) {
                // 查询已完成
                allList = this.baseMapper.findFinishedCollectPageWithDeduplication(dto);
            } else {
                // 查询待完成（默认）
                allList = this.baseMapper.findUnfinishedCollectPageWithDeduplication(dto);
            }
            totalCount = allList.size();
        } else if (dto.getProjectScheduling().equals(3)) {
            // 保持现在的分页查询，需要进行分组数据返回
            // 创建分页对象用于查询原始数据
            IPage<UniversityReadOverVo> dataPage = new Page<>(page.getCurrent(), page.getSize());
            IPage<UniversityReadOverVo> dataResult;

            // 根据查询类型调用不同的查询方法
            if (dto.getQueryType() != null && dto.getQueryType().equals(1)) {
                // 查询已完成
                dataResult = this.baseMapper.findFinishedCollectPage(dataPage, dto);
            } else {
                // 查询待完成（默认）
                dataResult = this.baseMapper.findUnfinishedCollectPage(dataPage, dto);
            }

            allList = dataResult.getRecords();
            totalCount = dataResult.getTotal();
        } else {
            // 其他projectScheduling值，使用原有逻辑
            // 创建分页对象用于查询原始数据
            IPage<UniversityReadOverVo> dataPage = new Page<>(page.getCurrent(), page.getSize());
            IPage<UniversityReadOverVo> dataResult;

            // 根据查询类型调用不同的查询方法
            if (dto.getQueryType() != null && dto.getQueryType().equals(1)) {
                // 查询已完成
                dataResult = this.baseMapper.findFinishedCollectPage(dataPage, dto);
            } else {
                // 查询待完成（默认）
                dataResult = this.baseMapper.findUnfinishedCollectPage(dataPage, dto);
            }

            allList = dataResult.getRecords();
            totalCount = dataResult.getTotal();
        }
        //学员id列表
        List<String> userIdList = allList.stream().map(UniversityReadOverVo::getUserId)
                .distinct().collect(Collectors.toList());

        if(!userIdList.isEmpty()){
            //用户信息列表
            List<TeachUserVO> userInfoList = sysUserService.findListByUserIds(userIdList);
            Map<String,TeachUserVO> userMap = userInfoList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            //部门信息列表
            List<TeachUserVO> userDepartmentList = sysUserService.findUserDepartmentByUserIds(userIdList);
            Map<String,TeachUserVO> departmentMap = userDepartmentList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            //设置用户信息
            for (UniversityReadOverVo readOverVo : allList) {
                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
                if(userVO!= null){
                    readOverVo.setUserName(userVO.getUserName());
                    readOverVo.setShopId(userVO.getShopId());
                    readOverVo.setShopName(userVO.getShopName());
                }
                TeachUserVO departmentVO = departmentMap.get(readOverVo.getUserId());
                if(departmentVO!= null){
                    readOverVo.setDepartmentName(departmentVO.getDepartmentName());
                }
            }
        }

        // 将 UniversityReadOverVo 转换为 UniversitySpotCheckCollectVo
        List<UniversitySpotCheckCollectVo> collectList = new ArrayList<>();

        // 按任务分组
        Map<String,List<UniversityReadOverVo>> taskMap = allList.stream().collect(Collectors.groupingBy(UniversityReadOverVo::getKey));
        for (Map.Entry<String, List<UniversityReadOverVo>> en : taskMap.entrySet()) {
            List<UniversityReadOverVo> list = en.getValue();
            UniversityReadOverVo universityReadOverVo = list.get(0);
            UniversitySpotCheckCollectVo collectVo = new UniversitySpotCheckCollectVo();
            collectVo.setActivityId(universityReadOverVo.getActivityId());
            collectVo.setTaskId(universityReadOverVo.getTaskId());
            collectVo.setTeachTool(universityReadOverVo.getTeachTool());
            collectVo.setTaskName(universityReadOverVo.getTaskName());
            collectVo.setProjectScheduling(universityReadOverVo.getProjectScheduling());
            collectVo.setTaskType(universityReadOverVo.getTaskType());

            if (collectVo.getProjectScheduling() == null || !collectVo.getProjectScheduling().equals(3)){
                //非循环模式-计算抽检率
                int count  = 0 ;
                for (UniversityReadOverVo readOverVo : list) {
                    if(!readOverVo.getRecordStatus().equals(0)){
                        count++;
                    }
                }
                BigDecimal spotCheckRate;
                if(count == list.size()){
                    spotCheckRate = BigDecimal.valueOf(100);
                }else{
                    spotCheckRate = BigDecimal.valueOf(count)
                            .divide(BigDecimal.valueOf(list.size()),8, RoundingMode.DOWN)
                            .multiply(BigDecimal.valueOf(100)).setScale(8, RoundingMode.DOWN);
                }
                collectVo.setSpotCheckRate(spotCheckRate);
                collectList.add(collectVo);
            }else{
                List<UniversitySpotCheckCollectVo> loopUnCollectList = new ArrayList<>();
                List<UniversitySpotCheckCollectVo> loopOverCollectList = new ArrayList<>();

                //循环模式
                Map<String, List<UniversityReadOverVo>> loopDateMap = list.stream().collect(Collectors.groupingBy(UniversityReadOverVo::getLoopDate));
                for (Map.Entry<String, List<UniversityReadOverVo>> entity : loopDateMap.entrySet()) {
                    List<UniversityReadOverVo> loopDateList = entity.getValue();
                    UniversityReadOverVo readOverChildVo = loopDateList.get(0);
                    UniversitySpotCheckCollectVo collecChildtVo = new UniversitySpotCheckCollectVo();
                    collecChildtVo.setActivityId(readOverChildVo.getActivityId());
                    collecChildtVo.setTaskId(readOverChildVo.getTaskId());
                    collecChildtVo.setTeachTool(readOverChildVo.getTeachTool());
                    collecChildtVo.setTaskName(readOverChildVo.getTaskName());
                    collecChildtVo.setProjectScheduling(readOverChildVo.getProjectScheduling());
                    collecChildtVo.setTaskType(readOverChildVo.getTaskType());
                    collecChildtVo.setLoopDate(readOverChildVo.getLoopDate());
                    //循环模式-计算抽检率
                    int count  = 0 ;
                    for (UniversityReadOverVo  childVo : loopDateList) {
                        if(!childVo.getRecordStatus().equals(0)){
                            count++;
                        }
                    }
                    BigDecimal spotCheckRate;
                    if(count == loopDateList.size()){
                        spotCheckRate = BigDecimal.valueOf(100);
                        loopOverCollectList.add(collecChildtVo);
                    }else{
                        spotCheckRate =  BigDecimal.valueOf(count)
                                .divide(BigDecimal.valueOf(loopDateList.size()),8, RoundingMode.DOWN)
                                .multiply(BigDecimal.valueOf(100)).setScale(8, RoundingMode.DOWN);
                        loopUnCollectList.add(collecChildtVo);
                    }
                    collecChildtVo.setSpotCheckRate(spotCheckRate);
                }

                loopUnCollectList=loopUnCollectList.stream().sorted(Comparator.comparing(UniversitySpotCheckCollectVo::getLoopDate).reversed()).collect(Collectors.toList());

                collectVo.setLoopUnCollectList(loopUnCollectList);
                collectVo.setLoopOverCollectList(loopOverCollectList);
                collectList.add(collectVo);
            }
        }

        // 创建分页结果
        IPage<UniversitySpotCheckCollectVo> result = new Page<>(page.getCurrent(), page.getSize(), dataResult.getTotal());
        result.setRecords(collectList);

        return result;
    }


    /**
     * 抽检查询列表
     * @param dto
     * @return
     */
    @Override
    public UniversitySpotCheckListVo findList(UniversitySpotCheckFindDTO dto) {
        UniversitySpotCheckListVo vo = new UniversitySpotCheckListVo();
        dto.setUserId(Common.getUserId());
        //门店转部门
        if (dto.getShopIdList()!=null&&dto.getShopIdList().size()>0) {
            QueryShopBaseInfoListDTO queryShopBaseInfoListDTO = new QueryShopBaseInfoListDTO();
            queryShopBaseInfoListDTO.setShopIdList(dto.getShopIdList());
            List<QueryShopBaseInfoListVO> baseInfoListVOS = FeignUtils.processFeignResult(shopBaseClient.findList(queryShopBaseInfoListDTO));
            if (baseInfoListVOS==null||baseInfoListVOS.isEmpty()) {
                return null;
            }
            dto.setDeptIdList(baseInfoListVOS.stream().map(QueryShopBaseInfoListVO::getSysDeptId).collect(Collectors.toList()));
        }
        //考试-未审批列表
        List<UniversityReadOverVo> unSpotCheckExamList = this.baseMapper.findUnSpotCheckExamList(dto);
        //考试-全部审批列表
        List<UniversityReadOverVo> overReadOverExamList = this.baseMapper.findOverSpotCheckExamList(dto);

        //作业-未审批列表
        List<UniversityReadOverVo> unSpotCheckHomeworkList = this.baseMapper.findUnSpotCheckHomeworkList(dto);
        //作业-全部审批列表
        List<UniversityReadOverVo> overReadOverHomeworkList = this.baseMapper.findOverSpotCheckHomeworkList(dto);

        //未审批列表
        List<UniversityReadOverVo> unList = new ArrayList<>();
        //已通过列表
        List<UniversityReadOverVo> passList = new ArrayList<>();
        //未通过列表
        List<UniversityReadOverVo> unPassList = new ArrayList<>();
        //全部列表
        List<UniversityReadOverVo> allList = new ArrayList<>();

        //设置-未审批列表
        unList.addAll(unSpotCheckExamList);
        unList.addAll(unSpotCheckHomeworkList);
        unList = unList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed())
                .collect(Collectors.toList());

        passList.addAll(overReadOverExamList.stream().filter(t->t.getRecordStatus().equals(2)).collect(Collectors.toList()));
        passList.addAll(overReadOverHomeworkList.stream().filter(t->t.getRecordStatus().equals(2)).collect(Collectors.toList()));
        passList = passList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed())
                .collect(Collectors.toList());

        unPassList.addAll(overReadOverExamList.stream().filter(t->t.getRecordStatus().equals(3)).collect(Collectors.toList()));
        unPassList.addAll(overReadOverHomeworkList.stream().filter(t->t.getRecordStatus().equals(3)).collect(Collectors.toList()));
        unPassList = unPassList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed())
                .collect(Collectors.toList());

        //设置-全部列表
        allList.addAll(unList);
        allList.addAll(passList);
        allList.addAll(unPassList);
        allList = allList.stream().sorted(Comparator.comparing(UniversityReadOverVo::getRecordTime).reversed())
                .collect(Collectors.toList());



        //学员id列表
        List<String> userIdList = allList.stream().map(UniversityReadOverVo::getUserId)
                .distinct().collect(Collectors.toList());

        if(!userIdList.isEmpty()){
            //用户信息列表
            List<TeachUserVO> userInfoList = sysUserService.findListByUserIds(userIdList);
            Map<String,TeachUserVO> userMap = userInfoList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            //部门信息列表
            List<TeachUserVO> userDepartmentList = sysUserService.findUserDepartmentByUserIds(userIdList);
            Map<String,TeachUserVO> departmentMap = userDepartmentList.stream()
                    .collect(Collectors.toMap(TeachUserVO::getId, t->t,(k1, k2)->k1));

            //设置用户信息
            for (UniversityReadOverVo readOverVo : unList) {
                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
                if(userVO!= null){
                    readOverVo.setUserName(userVO.getUserName());
                    readOverVo.setShopId(userVO.getShopId());
                    readOverVo.setShopName(userVO.getShopName());
                }
                TeachUserVO departmentVO = departmentMap.get(readOverVo.getUserId());
                if(departmentVO!= null){
                    readOverVo.setDepartmentName(departmentVO.getDepartmentName());
                }
            }
            //设置用户信息
            for (UniversityReadOverVo readOverVo : allList) {
                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
                if(userVO!= null){
                    readOverVo.setUserName(userVO.getUserName());
                    readOverVo.setShopId(userVO.getShopId());
                    readOverVo.setShopName(userVO.getShopName());
                }
                TeachUserVO departmentVO = departmentMap.get(readOverVo.getUserId());
                if(departmentVO!= null){
                    readOverVo.setDepartmentName(departmentVO.getDepartmentName());
                }
            }
            //设置用户信息
            for (UniversityReadOverVo readOverVo : passList) {
                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
                if(userVO!= null){
                    readOverVo.setUserName(userVO.getUserName());
                    readOverVo.setShopId(userVO.getShopId());
                    readOverVo.setShopName(userVO.getShopName());
                }
                TeachUserVO departmentVO = departmentMap.get(readOverVo.getUserId());
                if(departmentVO!= null){
                    readOverVo.setDepartmentName(departmentVO.getDepartmentName());
                }
            }

            //设置用户信息
            for (UniversityReadOverVo readOverVo : unPassList) {
                TeachUserVO userVO = userMap.get(readOverVo.getUserId());
                if(userVO!= null){
                    readOverVo.setUserName(userVO.getUserName());
                    readOverVo.setShopId(userVO.getShopId());
                    readOverVo.setShopName(userVO.getShopName());
                }
                TeachUserVO departmentVO = departmentMap.get(readOverVo.getUserId());
                if(departmentVO!= null){
                    readOverVo.setDepartmentName(departmentVO.getDepartmentName());
                }
            }
        }

        //考试任务
        UniversityTaskVO task = universityTaskService.queryById(dto.getTaskId());
        vo.setTask(task);


        //封装vo
        vo.setUnList(unList);
        vo.setPassList(passList);
        vo.setUnPassList(unPassList);
        vo.setAllList(allList);
        return vo;
    }


    /**
     * 获取抽检率
     * @param dto
     * @return
     */
    @Override
    public UniversitySpotCheckRateVo findSpotCheckRate(UniversitySpotCheckFindDTO dto) {
        UniversitySpotCheckRateVo vo = new UniversitySpotCheckRateVo();
        vo.setLoopDate(dto.getLoopDate());
        vo.setTaskId(dto.getTaskId());
        if(StringUtils.isBlank(dto.getTaskId())){
            throw new AppBizException("999000","任务id不能为空", StringUtils.EMPTY);
        }
        UniversityTaskVO task = universityTaskService.queryById(dto.getTaskId());
        if(task == null){
            return vo;
        }
        if(task.getTeachTool().equals(5)){
            //考试
            vo.setNeedSpotCheckRate(task.getTaskExam().getSpotCheckRate());
        }else  if(task.getTeachTool().equals(6)){
            //作业
            vo.setNeedSpotCheckRate(task.getTaskHomework().getSpotCheckRate());
        }


        // 为了保持兼容性，创建一个大的分页对象来获取所有数据
        IPage<UniversitySpotCheckCollectVo> largePage = new Page<>(1, 10000);

        // 分别查询待完成和已完成数据
        dto.setQueryType(0); // 待完成
        IPage<UniversitySpotCheckCollectVo> unfinishedResult = this.findCollect(largePage, dto);

        dto.setQueryType(1); // 已完成
        IPage<UniversitySpotCheckCollectVo> finishedResult = this.findCollect(largePage, dto);

        Integer unCount = (int) unfinishedResult.getTotal();
        Integer overCount = (int) finishedResult.getTotal();
        Integer allCount = unCount + overCount;

        if(allCount > 0) {
            vo.setNowSpotCheckRate(BigDecimal.valueOf(overCount).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(allCount),8,RoundingMode.DOWN));
            if(overCount.equals(allCount) ){
                vo.setNowSpotCheckRate(BigDecimal.valueOf(100));
                vo.setNeedSpotCheckNum(0);
            }else if(vo.getNeedSpotCheckRate().compareTo(BigDecimal.ZERO)>0){
                BigDecimal countDecimal =  BigDecimal.valueOf(allCount).multiply(vo.getNeedSpotCheckRate())
                        .divide(BigDecimal.valueOf(100),0,RoundingMode.UP);
                log.info("countDecimal ={}",countDecimal);
                Integer needCount = countDecimal.intValue();
                Integer needNum = needCount - overCount;
                if(needNum<0){
                    needNum = 0;
                }
                vo.setNeedSpotCheckNum(needNum);
            }
        }
        return vo;
    }




}



