package com.wxy.system.server.dao.university.project;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wxy.mybatis.plus.dao.MyBaseMapper;
import com.wxy.system.common.dto.university.record.spotcheck.UniversitySpotCheckFindDTO;
import com.wxy.system.common.vo.university.record.UniversityReadOverVo;
import com.wxy.system.common.vo.university.record.UniversitySpotCheckVo;
import com.wxy.system.common.vo.university.task.UniversityHomeworkUserSubmitDetSpotVo;
import com.wxy.system.server.po.university.record.UniversitySpotCheck;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 企业大学-抽检表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-08 10:51:05
 */
@Service
public interface UniversitySpotCheckDao extends MyBaseMapper<UniversitySpotCheck> {

    /**
     * 查询
     * @param id
     * @return
     */
    UniversitySpotCheck getById(@Param("id")String id);


    /**
     * 分页查询
     * @param page
     * @param dto
     * @return
     */
    IPage<UniversitySpotCheck> findPage(@Param("page")IPage page, @Param("dto") UniversitySpotCheckFindDTO dto);


    /**
     * 抽检列表
     * @param recordId
     * @return
     */
    List<UniversitySpotCheckVo> findListByRecordId(@Param("recordId") String recordId);

    List<UniversityHomeworkUserSubmitDetSpotVo> findByRecordId(@Param("id") String id);


    @InterceptorIgnore(tenantLine = "true")
    List<UniversityReadOverVo> findUnSpotCheckExamList(@Param("dto")UniversitySpotCheckFindDTO dto);
    @InterceptorIgnore(tenantLine = "true")
    List<UniversityReadOverVo> findOverSpotCheckExamList(@Param("dto")UniversitySpotCheckFindDTO dto);

    @InterceptorIgnore(tenantLine = "true")
    List<UniversityReadOverVo> findUnSpotCheckHomeworkList(@Param("dto")UniversitySpotCheckFindDTO dto);
    @InterceptorIgnore(tenantLine = "true")
    List<UniversityReadOverVo> findOverSpotCheckHomeworkList(@Param("dto")UniversitySpotCheckFindDTO dto);

    /**
     * 分页查询待完成抽检数据
     * @param page 分页参数
     * @param dto 查询条件
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<UniversityReadOverVo> findUnfinishedCollectPage(IPage<UniversityReadOverVo> page, @Param("dto")UniversitySpotCheckFindDTO dto);

    /**
     * 分页查询已完成抽检数据
     * @param page 分页参数
     * @param dto 查询条件
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<UniversityReadOverVo> findFinishedCollectPage(IPage<UniversityReadOverVo> page, @Param("dto")UniversitySpotCheckFindDTO dto);

    /**
     * 去重查询待完成抽检数据
     * @param dto 查询条件
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<UniversityReadOverVo> findUnfinishedCollectPageWithDeduplication(@Param("dto")UniversitySpotCheckFindDTO dto);

    /**
     * 去重查询已完成抽检数据
     * @param dto 查询条件
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<UniversityReadOverVo> findFinishedCollectPageWithDeduplication(@Param("dto")UniversitySpotCheckFindDTO dto);
}
