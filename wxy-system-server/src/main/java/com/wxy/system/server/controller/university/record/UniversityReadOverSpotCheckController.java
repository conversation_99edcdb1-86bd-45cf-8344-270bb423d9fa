package com.wxy.system.server.controller.university.record;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wxy.base.entity.DataResult;
import com.wxy.base.enumerate.HttpResult;
import com.wxy.base.exception.AppBizException;
import com.wxy.system.common.dto.university.record.UniversityReadOverSpotCheckFindDTO;
import com.wxy.system.common.dto.university.record.readover.UniversityExamReadOverFindDTO;
import com.wxy.system.common.dto.university.record.readover.UniversityExamReadOverSaveDTO;
import com.wxy.system.common.dto.university.record.readover.UniversityHomeworkReadOverFindDTO;
import com.wxy.system.common.dto.university.record.readover.UniversityHomeworkReadOverSaveDTO;
import com.wxy.system.common.vo.university.record.eaxm.UniversityExamReadOverAllVo;
import com.wxy.system.common.vo.university.record.eaxm.UniversityExamReadOverCollectVo;
import com.wxy.system.common.vo.university.record.eaxm.UniversityTestPaperRecordAllVo;
import com.wxy.system.common.vo.university.record.homework.UniversityHomeworkReadOverAllCollectVo;
import com.wxy.system.common.vo.university.record.homework.UniversityHomeworkReadOverCollectVo;
import com.wxy.system.common.vo.university.record.homework.UniversityHomeworkRecordAllVo;
import com.wxy.system.server.po.university.record.UniversityReadOverSpotCheck;
import com.wxy.system.server.service.university.record.IUniversityReadOverSpotCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * 企业大学-批阅信息和抽检(UniversityReadOverSpotCheck)前端控制器
 *
 * <AUTHOR>
 * @since 2024-09-20 17:06:11
 */

@Api(tags = "企业大学-批阅信息和抽检", produces = MediaType.APPLICATION_JSON_VALUE)
@Slf4j
@RestController
@RequestMapping("university-read-over-spot-check")
public class UniversityReadOverSpotCheckController  {

    @Resource
	private IUniversityReadOverSpotCheckService universityReadOverSpotCheckService;



    @ApiOperation(value = "考试批阅集合查询")
    @PostMapping(value="/exam/collect")
    public DataResult<UniversityExamReadOverCollectVo> examCollect(@RequestBody @Valid UniversityExamReadOverFindDTO dto) throws AppBizException {
        log.info("dto={}",dto);
        UniversityExamReadOverCollectVo vo = universityReadOverSpotCheckService.examCollect(dto);
        return new DataResult(vo);
    }

    @ApiOperation(value = "作业批阅集合查询")
    @PostMapping(value="/homework/collect")
    public DataResult<IPage<UniversityHomeworkReadOverCollectVo>> homeworkCollect(@RequestBody @Valid UniversityHomeworkReadOverFindDTO dto) throws AppBizException {
        log.info("dto={}",dto);
        UniversityHomeworkReadOverAllCollectVo vo = universityReadOverSpotCheckService.homeworkCollect(dto);
        return new DataResult(vo);
    }



    @ApiOperation(value = "考试批阅查询")
    @PostMapping(value="/exam/findList")
    public DataResult<UniversityExamReadOverAllVo> examFindList(@RequestBody @Valid UniversityExamReadOverFindDTO dto) throws AppBizException {
        log.info("dto={}",dto);
        UniversityExamReadOverAllVo vo = universityReadOverSpotCheckService.examFindList(dto);
        return new DataResult(vo);
    }

    @ApiOperation(value = "作业批阅查询")
    @PostMapping(value="/homework/findList")
    public DataResult<UniversityExamReadOverAllVo> homeworkFindList(@RequestBody @Valid UniversityHomeworkReadOverFindDTO dto) throws AppBizException {
        log.info("dto={}",dto);
        UniversityExamReadOverAllVo vo = universityReadOverSpotCheckService.homeworkFindList(dto);
        return new DataResult(vo);
    }





    @ApiOperation(value = "考试批阅保存")
    @PostMapping(value="/exam/readOver/save")
    public DataResult examReadOverSave(@RequestBody @Valid UniversityExamReadOverSaveDTO dto) throws AppBizException {
        log.info("dto={}",dto);
        universityReadOverSpotCheckService.examReadOverSave(dto,dto.getRecordId());
        return new DataResult(HttpResult.SUCCESS);
    }



    @ApiOperation(value = "作业批阅保存")
    @PostMapping(value="/homework/readOver/save")
    public DataResult homeworkReadOverSave(@RequestBody @Valid UniversityHomeworkReadOverSaveDTO dto) throws AppBizException {
        log.info("dto={}",dto);
        universityReadOverSpotCheckService.homeworkReadOverSave(dto,dto.getRecordId());
        return new DataResult(HttpResult.SUCCESS);
    }




    @ApiOperation(value = "查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name="id",value="id",dataType="String", paramType = "path",example="039f9120f20f0441509eaf823bffd32a",dataTypeClass = String.class)
    })
    @GetMapping(value = "/getById/{id}")
    public DataResult<UniversityReadOverSpotCheck> getById(@PathVariable String id)throws AppBizException {
        return new DataResult(universityReadOverSpotCheckService.getById(id));
    }




    @ApiOperation(value = "查询考试详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name="id",value="id",dataType="String", paramType = "path",example="039f9120f20f0441509eaf823bffd32a",dataTypeClass = String.class)
    })
    @GetMapping(value = "/getExam/{id}")
    public DataResult<UniversityTestPaperRecordAllVo> getExam(@PathVariable String id)throws AppBizException {
        UniversityTestPaperRecordAllVo vo = universityReadOverSpotCheckService.getExam(id);
        return new DataResult(vo);
    }

    @ApiOperation(value = "查询作业详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name="id",value="id",dataType="String", paramType = "path",example="039f9120f20f0441509eaf823bffd32a",dataTypeClass = String.class)
    })
    @GetMapping(value = "/getHomework/{id}")
    public DataResult<UniversityHomeworkRecordAllVo> getHomework(@PathVariable String id)throws AppBizException {
        UniversityHomeworkRecordAllVo vo = universityReadOverSpotCheckService.getHomework(id);
        return new DataResult(vo);
    }



    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name="pageNum",value="页数",dataType="Integer", paramType = "path",example="1",dataTypeClass = Integer.class),
            @ApiImplicitParam(name="pageSize",value="页码",dataType="Integer", paramType = "path",example="10",dataTypeClass = Integer.class)
    })
    @PostMapping(value = "/findPage/{pageNum}/{pageSize}")
    public DataResult<IPage<UniversityReadOverSpotCheck>> findPage(@PathVariable(name = "pageNum")Long pageNum,
                                              @PathVariable(name = "pageSize")Long pageSize,
                                              @RequestBody @Valid UniversityReadOverSpotCheckFindDTO dto)throws AppBizException {
        log.info("dto={}",dto);
        IPage page = new Page<>(pageNum,pageSize);
        return new DataResult(universityReadOverSpotCheckService.findPage(page,dto));
    }




//    @ApiOperation(value = "获取作业抽检人")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name="recordId",value="recordId",dataType="String", paramType = "path",example="1",dataTypeClass = String.class)
//    })
//    @PostMapping(value="/getHomeworkSpotCheck/{recordId}")
//    public DataResult getHomeworkSpotCheck(@PathVariable String recordId) throws AppBizException {
//        log.info("recordId={}",recordId);
//        List<String> list = universityReadOverSpotCheckService.getHomeworkSpotCheck(recordId, dto.getIsSave());
//        return new DataResult(list);
//    }
//
//
//    @ApiOperation(value = "获取考试抽检人")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name="recordId",value="recordId",dataType="String", paramType = "path",example="1",dataTypeClass = String.class)
//    })
//    @PostMapping(value="/getExamSpotCheck/{recordId}")
//    public DataResult getExamSpotCheck(@PathVariable String recordId) throws AppBizException {
//        log.info("recordId={}",recordId);
//        List<String> list = universityReadOverSpotCheckService.getExamSpotCheck(recordId, dto.getIsSave());
//        return new DataResult(list);
//    }



}
